#!/usr/bin/env python3
"""
Historical Performance Data Backfill Script

This script populates the performance_snapshots table with historical data
using the SQL-first approach for maximum performance.

Usage:
    python scripts/backfill_performance_data.py --start-date 2024-01-01 --end-date 2024-12-31
    python scripts/backfill_performance_data.py --days 90  # Last 90 days
    python scripts/backfill_performance_data.py --test     # Test with 5 schemes for 7 days
"""

import asyncio
import argparse
from datetime import date, datetime, timedelta
from typing import List, Optional
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from db.database import get_async_sessionmaker
from utils.logging_config import log_info, log_error
from utils.datetime_support import asia_kolkata


class PerformanceBackfiller:
    """Historical performance data backfill using SQL functions."""
    
    def __init__(self):
        self.async_sessionmaker = get_async_sessionmaker()
    
    async def get_available_schemes(self, limit: Optional[int] = None) -> List[int]:
        """Get list of schemes with sufficient NAV data."""
        async with self.async_sessionmaker() as session:
            query = text("""
                SELECT DISTINCT sm.schemecode
                FROM scheme_master sm
                WHERE sm.flag != 'D'
                  AND EXISTS (
                      SELECT 1 FROM navhist n 
                      WHERE n.schemecode = sm.schemecode 
                        AND n.navdate >= CURRENT_DATE - INTERVAL '2 years'
                        AND n.navrs > 0
                      HAVING COUNT(*) >= 100  -- At least 100 NAV records
                  )
                ORDER BY sm.schemecode
                """ + (f"LIMIT {limit}" if limit else ""))
            
            result = await session.execute(query)
            return [row[0] for row in result.fetchall()]
    
    async def get_trading_days(self, start_date: date, end_date: date) -> List[date]:
        """Get list of trading days (days with NAV data) in the date range."""
        async with self.async_sessionmaker() as session:
            query = text("""
                SELECT DISTINCT navdate
                FROM navhist
                WHERE navdate BETWEEN :start_date AND :end_date
                  AND navrs > 0
                ORDER BY navdate
            """)
            
            result = await session.execute(query, {
                "start_date": start_date,
                "end_date": end_date
            })
            return [row[0] for row in result.fetchall()]
    
    async def backfill_single_date(self, calc_date: date) -> dict:
        """Backfill performance data for a single date using SQL function."""
        async with self.async_sessionmaker() as session:
            try:
                log_info(f"Backfilling {calc_date}...")
                
                # Use the SQL function to calculate and store all schemes for this date
                query = text("SELECT * FROM refresh_performance_data(:calc_date)")
                
                result = await session.execute(query, {"calc_date": calc_date})
                stats = result.fetchone()
                
                if stats:
                    stats_dict = dict(stats._mapping)
                    log_info(f"✅ {calc_date}: {stats_dict['schemes_successful']}/{stats_dict['schemes_processed']} schemes successful")
                    return stats_dict
                else:
                    log_error(f"❌ {calc_date}: No result from refresh_performance_data")
                    return {"schemes_processed": 0, "schemes_successful": 0, "schemes_failed": 1}
                    
            except Exception as e:
                log_error(f"❌ {calc_date}: Error - {e}")
                await session.rollback()
                return {"schemes_processed": 0, "schemes_successful": 0, "schemes_failed": 1}
    
    async def backfill_date_range(
        self, 
        start_date: date, 
        end_date: date,
        skip_existing: bool = True
    ) -> dict:
        """Backfill performance data for a date range."""
        
        # Get trading days in the range
        trading_days = await self.get_trading_days(start_date, end_date)
        log_info(f"Found {len(trading_days)} trading days between {start_date} and {end_date}")
        
        if skip_existing:
            # Check which dates already have data
            async with self.async_sessionmaker() as session:
                query = text("""
                    SELECT DISTINCT date 
                    FROM performance_snapshots 
                    WHERE date BETWEEN :start_date AND :end_date
                """)
                result = await session.execute(query, {
                    "start_date": start_date,
                    "end_date": end_date
                })
                existing_dates = {row[0] for row in result.fetchall()}
                
                # Filter out existing dates
                trading_days = [d for d in trading_days if d not in existing_dates]
                log_info(f"Skipping {len(existing_dates)} dates with existing data")
                log_info(f"Processing {len(trading_days)} remaining dates")
        
        # Process each trading day
        total_stats = {
            "dates_processed": 0,
            "dates_successful": 0,
            "total_schemes_processed": 0,
            "total_schemes_successful": 0,
            "total_schemes_failed": 0
        }
        
        for i, calc_date in enumerate(trading_days, 1):
            log_info(f"Progress: {i}/{len(trading_days)} - Processing {calc_date}")
            
            stats = await self.backfill_single_date(calc_date)
            
            total_stats["dates_processed"] += 1
            total_stats["total_schemes_processed"] += stats.get("schemes_processed", 0)
            total_stats["total_schemes_successful"] += stats.get("schemes_successful", 0)
            total_stats["total_schemes_failed"] += stats.get("schemes_failed", 0)
            
            if stats.get("schemes_successful", 0) > 0:
                total_stats["dates_successful"] += 1
            
            # Progress update every 10 dates
            if i % 10 == 0:
                success_rate = (total_stats["total_schemes_successful"] / 
                              max(total_stats["total_schemes_processed"], 1)) * 100
                log_info(f"📊 Progress: {i}/{len(trading_days)} dates, "
                        f"{total_stats['total_schemes_successful']} schemes successful "
                        f"({success_rate:.1f}% success rate)")
        
        return total_stats
    
    async def test_backfill(self) -> dict:
        """Test backfill with a small dataset."""
        log_info("🧪 Running test backfill (5 schemes, last 7 days)")
        
        # Get 5 test schemes
        test_schemes = await self.get_available_schemes(limit=5)
        if not test_schemes:
            log_error("No schemes available for testing")
            return {}
        
        log_info(f"Test schemes: {test_schemes}")
        
        # Test last 7 days
        end_date = date.today()
        start_date = end_date - timedelta(days=7)
        
        return await self.backfill_date_range(start_date, end_date, skip_existing=False)


async def main():
    """Main backfill script."""
    parser = argparse.ArgumentParser(description="Backfill historical performance data")
    parser.add_argument("--start-date", type=str, help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, help="End date (YYYY-MM-DD)")
    parser.add_argument("--days", type=int, help="Number of days back from today")
    parser.add_argument("--test", action="store_true", help="Run test backfill")
    parser.add_argument("--force", action="store_true", help="Overwrite existing data")
    
    args = parser.parse_args()
    
    backfiller = PerformanceBackfiller()
    
    try:
        if args.test:
            # Test mode
            stats = await backfiller.test_backfill()
        
        elif args.days:
            # Last N days
            end_date = date.today()
            start_date = end_date - timedelta(days=args.days)
            log_info(f"🚀 Backfilling last {args.days} days: {start_date} to {end_date}")
            stats = await backfiller.backfill_date_range(start_date, end_date, skip_existing=not args.force)
        
        elif args.start_date and args.end_date:
            # Specific date range
            start_date = datetime.strptime(args.start_date, "%Y-%m-%d").date()
            end_date = datetime.strptime(args.end_date, "%Y-%m-%d").date()
            log_info(f"🚀 Backfilling date range: {start_date} to {end_date}")
            stats = await backfiller.backfill_date_range(start_date, end_date, skip_existing=not args.force)
        
        else:
            # Default: last 30 days
            end_date = date.today()
            start_date = end_date - timedelta(days=30)
            log_info(f"🚀 Backfilling last 30 days: {start_date} to {end_date}")
            stats = await backfiller.backfill_date_range(start_date, end_date, skip_existing=not args.force)
        
        # Print final summary
        log_info("🎉 Backfill completed!")
        log_info(f"📊 Final Stats:")
        log_info(f"   Dates processed: {stats.get('dates_processed', 0)}")
        log_info(f"   Dates successful: {stats.get('dates_successful', 0)}")
        log_info(f"   Total schemes processed: {stats.get('total_schemes_processed', 0)}")
        log_info(f"   Total schemes successful: {stats.get('total_schemes_successful', 0)}")
        log_info(f"   Total schemes failed: {stats.get('total_schemes_failed', 0)}")
        
        if stats.get('total_schemes_processed', 0) > 0:
            success_rate = (stats['total_schemes_successful'] / stats['total_schemes_processed']) * 100
            log_info(f"   Success rate: {success_rate:.1f}%")
        
        # Show sample data
        log_info("📋 Sample of created data:")
        async with backfiller.async_sessionmaker() as session:
            query = text("""
                SELECT schemecode, date, fund_name, nav, perf_1y, cagr_3y
                FROM performance_snapshots
                ORDER BY date DESC, schemecode
                LIMIT 5
            """)
            result = await session.execute(query)
            for row in result.fetchall():
                log_info(f"   {row.schemecode}: {row.fund_name} | {row.date} | NAV: {row.nav} | 1Y: {row.perf_1y}% | 3Y CAGR: {row.cagr_3y}%")
    
    except Exception as e:
        log_error(f"❌ Backfill failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
