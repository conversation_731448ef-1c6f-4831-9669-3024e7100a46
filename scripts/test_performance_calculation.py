#!/usr/bin/env python3
"""
Quick Performance Calculation Test Script

This script tests the performance calculation system with a few schemes
to verify everything is working correctly.

Usage:
    python scripts/test_performance_calculation.py
"""

import asyncio
import sys
import os
from datetime import date, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from db.database import get_async_sessionmaker
from src.services.performance.sql_calculator import SQLPerformanceCalculator
from utils.logging_config import log_info, log_error


async def test_nav_functions():
    """Test the basic NAV retrieval functions."""
    log_info("🧪 Testing NAV functions...")
    
    async_sessionmaker = get_async_sessionmaker()
    async with async_sessionmaker() as session:
        # Get a test scheme with recent data
        query = text("""
            SELECT DISTINCT sm.schemecode, sm.scheme_name, MAX(n.navdate) as latest_nav
            FROM scheme_master sm
            JOIN navhist n ON sm.schemecode = n.schemecode
            WHERE sm.flag != 'D' AND n.navdate >= CURRENT_DATE - INTERVAL '30 days'
            GROUP BY sm.schemecode, sm.scheme_name
            ORDER BY latest_nav DESC
            LIMIT 1
        """)
        result = await session.execute(query)
        test_scheme = result.fetchone()
        
        if not test_scheme:
            log_error("❌ No schemes with recent NAV data found")
            return False
        
        schemecode = test_scheme.schemecode
        scheme_name = test_scheme.scheme_name
        log_info(f"📊 Testing with scheme: {schemecode} - {scheme_name}")
        
        # Test get_nav_safe function
        test_date = date.today()
        query = text("SELECT get_nav_safe(:schemecode, :test_date)")
        result = await session.execute(query, {"schemecode": schemecode, "test_date": test_date})
        current_nav = result.scalar()
        
        if current_nav:
            log_info(f"✅ get_nav_safe: Current NAV = {current_nav}")
        else:
            log_error(f"❌ get_nav_safe: No NAV found for {test_date}")
            return False
        
        # Test get_nav_points function
        query = text("SELECT * FROM get_nav_points(:schemecode, :test_date)")
        result = await session.execute(query, {"schemecode": schemecode, "test_date": test_date})
        nav_points = result.fetchone()
        
        if nav_points:
            log_info(f"✅ get_nav_points: Found NAV points")
            log_info(f"   Current: {nav_points.nav_current}")
            log_info(f"   1 Week ago: {nav_points.nav_1w}")
            log_info(f"   1 Year ago: {nav_points.nav_1y}")
        else:
            log_error("❌ get_nav_points: No NAV points found")
            return False
        
        return True


async def test_calculation_functions():
    """Test the performance calculation functions."""
    log_info("🧮 Testing calculation functions...")
    
    async_sessionmaker = get_async_sessionmaker()
    async with async_sessionmaker() as session:
        # Test calculate_return function
        query = text("SELECT calculate_return(110.0, 100.0)")
        result = await session.execute(query)
        return_value = result.scalar()
        
        if abs(return_value - 10.0) < 0.01:  # Should be 10%
            log_info(f"✅ calculate_return: 110 vs 100 = {return_value}%")
        else:
            log_error(f"❌ calculate_return: Expected 10%, got {return_value}%")
            return False
        
        # Test calculate_cagr function
        query = text("SELECT calculate_cagr(121.0, 100.0, 2)")
        result = await session.execute(query)
        cagr_value = result.scalar()
        
        if abs(cagr_value - 10.0) < 0.01:  # Should be 10% CAGR
            log_info(f"✅ calculate_cagr: 121 vs 100 over 2 years = {cagr_value}%")
        else:
            log_error(f"❌ calculate_cagr: Expected 10%, got {cagr_value}%")
            return False
        
        return True


async def test_master_function():
    """Test the master performance calculation function."""
    log_info("🎯 Testing master calculation function...")
    
    async_sessionmaker = get_async_sessionmaker()
    async with async_sessionmaker() as session:
        # Get a test scheme
        query = text("""
            SELECT DISTINCT sm.schemecode, sm.scheme_name
            FROM scheme_master sm
            JOIN navhist n ON sm.schemecode = n.schemecode
            WHERE sm.flag != 'D' 
              AND n.navdate >= CURRENT_DATE - INTERVAL '30 days'
              AND EXISTS (
                  SELECT 1 FROM navhist n2 
                  WHERE n2.schemecode = sm.schemecode 
                    AND n2.navdate >= CURRENT_DATE - INTERVAL '1 year'
                  HAVING COUNT(*) >= 50
              )
            ORDER BY sm.schemecode
            LIMIT 1
        """)
        result = await session.execute(query)
        test_scheme = result.fetchone()
        
        if not test_scheme:
            log_error("❌ No suitable test scheme found")
            return False
        
        schemecode = test_scheme.schemecode
        scheme_name = test_scheme.scheme_name
        test_date = date.today()
        
        log_info(f"📊 Testing master function with: {schemecode} - {scheme_name}")
        
        # Test calculate_performance_metrics function
        query = text("SELECT * FROM calculate_performance_metrics(:schemecode, :test_date)")
        result = await session.execute(query, {"schemecode": schemecode, "test_date": test_date})
        metrics = result.fetchone()
        
        if metrics:
            log_info(f"✅ calculate_performance_metrics: Success!")
            log_info(f"   Fund: {metrics.fund_name}")
            log_info(f"   NAV: {metrics.nav}")
            log_info(f"   1W Return: {metrics.perf_1w}%")
            log_info(f"   1M Return: {metrics.perf_1m}%")
            log_info(f"   1Y Return: {metrics.perf_1y}%")
            log_info(f"   3Y CAGR: {metrics.cagr_3y}%")
            log_info(f"   Asset Class: {metrics.asset_class}")
            return True
        else:
            log_error("❌ calculate_performance_metrics: No result")
            return False


async def test_sql_calculator():
    """Test the Python SQL calculator wrapper."""
    log_info("🐍 Testing Python SQL calculator...")
    
    calculator = SQLPerformanceCalculator(None)  # Will create its own session
    
    async_sessionmaker = get_async_sessionmaker()
    async with async_sessionmaker() as session:
        calculator.session = session
        
        # Get a test scheme
        query = text("""
            SELECT schemecode FROM scheme_master sm
            WHERE flag != 'D' AND EXISTS (
                SELECT 1 FROM navhist n 
                WHERE n.schemecode = sm.schemecode 
                  AND n.navdate >= CURRENT_DATE - INTERVAL '30 days'
            )
            LIMIT 1
        """)
        result = await session.execute(query)
        test_schemecode = result.scalar()
        
        if not test_schemecode:
            log_error("❌ No test scheme available")
            return False
        
        # Test single scheme calculation
        result = await calculator.calculate_single_scheme(test_schemecode, date.today())
        
        if result:
            log_info(f"✅ SQLPerformanceCalculator.calculate_single_scheme: Success!")
            log_info(f"   Scheme: {result.get('schemecode')}")
            log_info(f"   Fund: {result.get('fund_name')}")
            log_info(f"   1Y Return: {result.get('perf_1y')}%")
            return True
        else:
            log_error("❌ SQLPerformanceCalculator.calculate_single_scheme: Failed")
            return False


async def test_data_insertion():
    """Test inserting calculated data into performance_snapshots."""
    log_info("💾 Testing data insertion...")
    
    async_sessionmaker = get_async_sessionmaker()
    async with async_sessionmaker() as session:
        # Get count before
        query = text("SELECT COUNT(*) FROM performance_snapshots")
        result = await session.execute(query)
        count_before = result.scalar()
        
        # Run refresh for today (small test)
        test_date = date.today()
        query = text("SELECT * FROM refresh_performance_data(:test_date)")
        result = await session.execute(query, {"test_date": test_date})
        stats = result.fetchone()
        
        if stats:
            log_info(f"✅ refresh_performance_data: Success!")
            log_info(f"   Schemes processed: {stats.schemes_processed}")
            log_info(f"   Schemes successful: {stats.schemes_successful}")
            log_info(f"   Schemes failed: {stats.schemes_failed}")
            log_info(f"   Execution time: {stats.execution_time_seconds:.2f} seconds")
            
            # Check count after
            result = await session.execute(query)
            count_after = result.scalar()
            new_records = count_after - count_before
            log_info(f"   New records created: {new_records}")
            
            return stats.schemes_successful > 0
        else:
            log_error("❌ refresh_performance_data: No result")
            return False


async def main():
    """Run all tests."""
    log_info("🚀 Starting Performance System Tests")
    log_info("=" * 50)
    
    tests = [
        ("NAV Functions", test_nav_functions),
        ("Calculation Functions", test_calculation_functions),
        ("Master Function", test_master_function),
        ("SQL Calculator", test_sql_calculator),
        ("Data Insertion", test_data_insertion),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        log_info(f"\n🧪 Running: {test_name}")
        log_info("-" * 30)
        
        try:
            success = await test_func()
            results[test_name] = success
            
            if success:
                log_info(f"✅ {test_name}: PASSED")
            else:
                log_error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            log_error(f"❌ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    log_info("\n" + "=" * 50)
    log_info("🎯 TEST SUMMARY")
    log_info("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        log_info(f"   {test_name}: {status}")
    
    log_info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        log_info("🎉 All tests passed! Your performance system is ready!")
        
        # Show next steps
        log_info("\n📋 Next Steps:")
        log_info("   1. Run historical backfill:")
        log_info("      python scripts/backfill_performance_data.py --test")
        log_info("   2. Run for last 30 days:")
        log_info("      python scripts/backfill_performance_data.py --days 30")
        log_info("   3. Check your data:")
        log_info("      SELECT * FROM performance_snapshots ORDER BY date DESC LIMIT 10;")
    else:
        log_error("❌ Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    asyncio.run(main())
