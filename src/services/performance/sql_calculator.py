"""
SQL-First Performance Calculator

This module leverages PostgreSQL functions to do all the heavy lifting.
Python code is minimal - just orchestration and error handling.
"""

from datetime import date, datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from utils.logging_config import log_info, log_error


class SQLPerformanceCalculator:
    """
    Database-first performance calculator.
    
    All calculations are done in PostgreSQL using the functions from migrations:
    - performance_002: NAV functions
    - performance_003: Calculation functions  
    - performance_004: Master functions
    - performance_005: Continuous aggregates
    """
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def calculate_single_scheme(
        self, 
        schemecode: int, 
        calculation_date: date = None
    ) -> Optional[Dict[str, Any]]:
        """
        Calculate performance for a single scheme using SQL function.
        
        Args:
            schemecode: Fund identifier
            calculation_date: Date for calculations (defaults to today)
            
        Returns:
            Dictionary with all performance metrics or None if failed
        """
        if calculation_date is None:
            calculation_date = date.today()
        
        try:
            # Let PostgreSQL do ALL the work!
            query = text("""
                SELECT * FROM calculate_performance_metrics(:schemecode, :calc_date)
            """)
            
            result = await self.session.execute(query, {
                "schemecode": schemecode,
                "calc_date": calculation_date
            })
            
            row = result.fetchone()
            if row:
                return dict(row._mapping)
            else:
                log_error(f"No performance data calculated for scheme {schemecode}")
                return None
                
        except Exception as e:
            log_error(f"Error calculating performance for scheme {schemecode}: {e}")
            return None
    
    async def calculate_daily_snapshots(
        self, 
        calculation_date: date = None
    ) -> Dict[str, int]:
        """
        Calculate and store daily snapshots for all active schemes.
        
        Args:
            calculation_date: Date for calculations (defaults to today)
            
        Returns:
            Dictionary with processing statistics
        """
        if calculation_date is None:
            calculation_date = date.today()
        
        try:
            # Single SQL function call does everything!
            query = text("""
                SELECT * FROM refresh_performance_data(:calc_date)
            """)
            
            result = await self.session.execute(query, {
                "calc_date": calculation_date
            })
            
            row = result.fetchone()
            if row:
                stats = dict(row._mapping)
                log_info(f"Performance calculation completed: {stats}")
                return stats
            else:
                return {"schemes_processed": 0, "schemes_successful": 0, "schemes_failed": 0}
                
        except Exception as e:
            log_error(f"Error in daily performance calculation: {e}")
            await self.session.rollback()
            return {"schemes_processed": 0, "schemes_successful": 0, "schemes_failed": 1}
    
    async def get_calculation_summary(
        self, 
        calculation_date: date = None
    ) -> Dict[str, Any]:
        """
        Get summary statistics for a calculation date.
        
        Args:
            calculation_date: Date to summarize (defaults to today)
            
        Returns:
            Dictionary with summary statistics
        """
        if calculation_date is None:
            calculation_date = date.today()
        
        try:
            query = text("""
                SELECT * FROM get_performance_summary(:calc_date)
            """)
            
            result = await self.session.execute(query, {
                "calc_date": calculation_date
            })
            
            row = result.fetchone()
            return dict(row._mapping) if row else {}
            
        except Exception as e:
            log_error(f"Error getting calculation summary: {e}")
            return {}
    
    async def validate_scheme_data(
        self, 
        schemecode: int, 
        calculation_date: date = None
    ) -> List[Dict[str, Any]]:
        """
        Validate data availability for a scheme.
        
        Args:
            schemecode: Fund identifier
            calculation_date: Date for validation (defaults to today)
            
        Returns:
            List of validation results
        """
        if calculation_date is None:
            calculation_date = date.today()
        
        try:
            query = text("""
                SELECT * FROM validate_performance_data(:schemecode, :calc_date)
            """)
            
            result = await self.session.execute(query, {
                "schemecode": schemecode,
                "calc_date": calculation_date
            })
            
            return [dict(row._mapping) for row in result.fetchall()]
            
        except Exception as e:
            log_error(f"Error validating scheme data: {e}")
            return []
    
    async def get_category_averages(
        self, 
        asset_class: str, 
        calculation_date: date = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get category averages using TimescaleDB continuous aggregate.
        
        Args:
            asset_class: Asset class to get averages for
            calculation_date: Date for averages (defaults to today)
            
        Returns:
            Dictionary with category averages
        """
        if calculation_date is None:
            calculation_date = date.today()
        
        try:
            # Query the auto-updating continuous aggregate
            query = text("""
                SELECT 
                    asset_class,
                    avg_perf_1w,
                    avg_perf_1m,
                    avg_perf_3m,
                    avg_perf_6m,
                    avg_perf_1y,
                    avg_cagr_3y,
                    avg_cagr_5y,
                    avg_cagr_10y,
                    fund_count,
                    stddev_perf_1y,
                    max_perf_1y,
                    min_perf_1y
                FROM performance_category_averages
                WHERE asset_class = :asset_class 
                  AND day = :calc_date
            """)
            
            result = await self.session.execute(query, {
                "asset_class": asset_class,
                "calc_date": calculation_date
            })
            
            row = result.fetchone()
            return dict(row._mapping) if row else None
            
        except Exception as e:
            log_error(f"Error getting category averages: {e}")
            return None
    
    async def get_market_summary(
        self, 
        calculation_date: date = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get overall market summary using continuous aggregate.
        
        Args:
            calculation_date: Date for summary (defaults to today)
            
        Returns:
            Dictionary with market summary
        """
        if calculation_date is None:
            calculation_date = date.today()
        
        try:
            query = text("""
                SELECT * FROM performance_daily_summary
                WHERE day = :calc_date
            """)
            
            result = await self.session.execute(query, {
                "calc_date": calculation_date
            })
            
            row = result.fetchone()
            return dict(row._mapping) if row else None
            
        except Exception as e:
            log_error(f"Error getting market summary: {e}")
            return None
    
    async def get_top_performers(
        self, 
        asset_class: str = None,
        calculation_date: date = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get top performers using continuous aggregate.
        
        Args:
            asset_class: Asset class filter (optional)
            calculation_date: Date for top performers (defaults to today)
            
        Returns:
            Dictionary with top performer arrays
        """
        if calculation_date is None:
            calculation_date = date.today()
        
        try:
            if asset_class:
                query = text("""
                    SELECT * FROM performance_top_performers
                    WHERE asset_class = :asset_class AND day = :calc_date
                """)
                params = {"asset_class": asset_class, "calc_date": calculation_date}
            else:
                # Get overall top performers across all asset classes
                query = text("""
                    SELECT 
                        'All' as asset_class,
                        ARRAY_AGG(DISTINCT unnest ORDER BY unnest LIMIT 10) as top_10_1y_schemes
                    FROM (
                        SELECT unnest(top_10_1y_schemes) 
                        FROM performance_top_performers 
                        WHERE day = :calc_date
                    ) t
                """)
                params = {"calc_date": calculation_date}
            
            result = await self.session.execute(query, params)
            row = result.fetchone()
            return dict(row._mapping) if row else None
            
        except Exception as e:
            log_error(f"Error getting top performers: {e}")
            return None


class SQLBatchCalculator:
    """Batch processing using SQL functions."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.calculator = SQLPerformanceCalculator(session)
    
    async def calculate_batch_schemes(
        self, 
        schemecodes: List[int], 
        calculation_date: date = None
    ) -> List[Dict[str, Any]]:
        """
        Calculate performance for multiple schemes using SQL batch function.
        
        Args:
            schemecodes: List of fund identifiers
            calculation_date: Date for calculations
            
        Returns:
            List of performance records
        """
        if calculation_date is None:
            calculation_date = date.today()
        
        try:
            # PostgreSQL array processing - super efficient!
            query = text("""
                SELECT * FROM calculate_batch_performance(:schemecodes, :calc_date)
            """)
            
            result = await self.session.execute(query, {
                "schemecodes": schemecodes,
                "calc_date": calculation_date
            })
            
            return [dict(row._mapping) for row in result.fetchall()]
            
        except Exception as e:
            log_error(f"Error in batch calculation: {e}")
            return []
    
    async def backfill_date_range(
        self, 
        start_date: date, 
        end_date: date,
        schemecodes: List[int] = None
    ) -> Dict[str, int]:
        """
        Backfill performance data for a date range.
        
        Args:
            start_date: Start of date range
            end_date: End of date range  
            schemecodes: Optional list of specific schemes
            
        Returns:
            Dictionary with backfill statistics
        """
        total_processed = 0
        total_successful = 0
        
        current_date = start_date
        while current_date <= end_date:
            try:
                if schemecodes:
                    # Backfill specific schemes
                    results = await self.calculate_batch_schemes(schemecodes, current_date)
                    total_processed += len(schemecodes)
                    total_successful += len(results)
                else:
                    # Backfill all schemes for this date
                    stats = await self.calculator.calculate_daily_snapshots(current_date)
                    total_processed += stats.get("schemes_processed", 0)
                    total_successful += stats.get("schemes_successful", 0)
                
                log_info(f"Backfilled {current_date}: {total_successful}/{total_processed} successful")
                
            except Exception as e:
                log_error(f"Error backfilling {current_date}: {e}")
            
            current_date = current_date.replace(day=current_date.day + 1)
        
        return {
            "total_processed": total_processed,
            "total_successful": total_successful,
            "total_failed": total_processed - total_successful
        }
