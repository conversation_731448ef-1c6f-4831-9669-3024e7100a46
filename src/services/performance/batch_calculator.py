"""Batch performance calculation for multiple schemes."""

import asyncio
from datetime import date
from typing import List, Dict, Optional
from decimal import Decimal

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from config.logger import log_info, log_error

from .calculator import PerformanceCalculator, PerformanceMetrics


class BatchPerformanceCalculator:
    """Efficient batch calculator for multiple schemes."""

    def __init__(self, session: AsyncSession):
        self.session = session
        self.calculator = PerformanceCalculator(session)

    async def get_active_schemes(self, asset_class: Optional[str] = None) -> List[int]:
        """
        Get list of active scheme codes.

        Args:
            asset_class: Filter by asset class (optional)

        Returns:
            List of active scheme codes
        """
        try:
            base_query = """
                SELECT DISTINCT sm.schemecode
                FROM scheme_master sm
                INNER JOIN navhist n ON sm.schemecode = n.schemecode
                WHERE sm.flag != 'D'
                  AND n.navdate >= CURRENT_DATE - INTERVAL '30 days'
            """

            if asset_class:
                base_query += """
                    AND EXISTS (
                        SELECT 1 FROM scheme_details sd
                        INNER JOIN sclass_mst sc ON sd.classcode = sc.classcode
                        WHERE sd.schemecode = sm.schemecode
                          AND sc.asset_type = :asset_class
                    )
                """

            base_query += " ORDER BY sm.schemecode"

            query = text(base_query)
            params = {"asset_class": asset_class} if asset_class else {}

            result = await self.session.execute(query, params)
            return [row[0] for row in result.fetchall()]

        except Exception as e:
            log_error(f"Error getting active schemes: {e}")
            return []

    async def calculate_batch_metrics(
        self,
        schemecodes: List[int],
        calculation_date: Optional[date] = None,
        batch_size: int = 50,
        include_relative_performance: bool = True,
    ) -> List[PerformanceMetrics]:
        """
        Calculate performance metrics for multiple schemes in batches.

        Args:
            schemecodes: List of scheme codes to process
            calculation_date: Date for calculations (default: today)
            batch_size: Number of schemes to process concurrently
            include_relative_performance: Whether to calculate relative performance

        Returns:
            List of PerformanceMetrics objects
        """
        if calculation_date is None:
            calculation_date = date.today()

        log_info(f"Starting batch calculation for {len(schemecodes)} schemes")

        all_metrics = []

        # Process in batches to avoid overwhelming the database
        for i in range(0, len(schemecodes), batch_size):
            batch = schemecodes[i : i + batch_size]
            log_info(
                f"Processing batch {i//batch_size + 1}: schemes {i+1}-{min(i+batch_size, len(schemecodes))}"
            )

            # Calculate metrics for this batch concurrently
            batch_tasks = [
                self.calculator.calculate_all_metrics(schemecode, calculation_date)
                for schemecode in batch
            ]

            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # Filter out exceptions and collect valid results
            valid_results = []
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    log_error(
                        f"Error calculating metrics for scheme {batch[j]}: {result}"
                    )
                else:
                    valid_results.append(result)

            all_metrics.extend(valid_results)

        # Add relative performance if requested
        if include_relative_performance:
            log_info("Adding relative performance metrics")
            all_metrics = await self._add_batch_relative_performance(all_metrics)

        log_info(
            f"Completed batch calculation. Processed {len(all_metrics)} schemes successfully"
        )
        return all_metrics

    async def _add_batch_relative_performance(
        self, metrics_list: List[PerformanceMetrics]
    ) -> List[PerformanceMetrics]:
        """
        Add relative performance to a batch of metrics efficiently.

        Args:
            metrics_list: List of PerformanceMetrics to enhance

        Returns:
            Enhanced list with relative performance
        """
        try:
            # Group schemes by asset class for efficient subcategory average calculation
            asset_class_groups = {}
            for metrics in metrics_list:
                if metrics.asset_class:
                    if metrics.asset_class not in asset_class_groups:
                        asset_class_groups[metrics.asset_class] = []
                    asset_class_groups[metrics.asset_class].append(metrics)

            # Calculate subcategory averages for each asset class
            subcategory_averages = {}
            for asset_class in asset_class_groups.keys():
                if asset_class_groups[asset_class]:  # Check if list is not empty
                    calculation_date = asset_class_groups[asset_class][
                        0
                    ].calculation_date
                    subcategory_averages[asset_class] = (
                        await self.calculator.calculate_subcategory_averages(
                            asset_class, calculation_date
                        )
                    )

            # Apply relative performance to each metrics object
            enhanced_metrics = []
            for metrics in metrics_list:
                if metrics.asset_class and metrics.asset_class in subcategory_averages:
                    subcategory_avgs = subcategory_averages[metrics.asset_class]

                    # Calculate relative performance
                    if metrics.perf_1y and subcategory_avgs["avg_perf_1y"]:
                        metrics.ret_vs_subcat_1y = (
                            metrics.perf_1y - subcategory_avgs["avg_perf_1y"]
                        )

                    if metrics.cagr_3y and subcategory_avgs["avg_cagr_3y"]:
                        metrics.ret_vs_subcat_3y = (
                            metrics.cagr_3y - subcategory_avgs["avg_cagr_3y"]
                        )

                    if metrics.cagr_5y and subcategory_avgs["avg_cagr_5y"]:
                        metrics.ret_vs_subcat_5y = (
                            metrics.cagr_5y - subcategory_avgs["avg_cagr_5y"]
                        )

                    if metrics.cagr_10y and subcategory_avgs["avg_cagr_10y"]:
                        metrics.ret_vs_subcat_10y = (
                            metrics.cagr_10y - subcategory_avgs["avg_cagr_10y"]
                        )

                enhanced_metrics.append(metrics)

            return enhanced_metrics

        except Exception as e:
            log_error(f"Error adding batch relative performance: {e}")
            return metrics_list

    async def calculate_and_store_daily_snapshots(
        self,
        calculation_date: Optional[date] = None,
        asset_class_filter: Optional[str] = None,
    ) -> int:
        """
        Calculate and store daily performance snapshots for all active schemes.

        This method is designed to be run daily to populate the performance data.

        Args:
            calculation_date: Date for calculations (default: today)
            asset_class_filter: Filter by asset class (optional)

        Returns:
            Number of schemes processed successfully
        """
        if calculation_date is None:
            calculation_date = date.today()

        log_info(f"Starting daily snapshot calculation for {calculation_date}")

        try:
            # Get active schemes
            active_schemes = await self.get_active_schemes(asset_class_filter)

            if not active_schemes:
                log_info("No active schemes found")
                return 0

            # Calculate metrics for all schemes
            all_metrics = await self.calculate_batch_metrics(
                active_schemes,
                calculation_date,
                batch_size=100,
                include_relative_performance=True,
            )

            # Store results (this would typically insert into a table or refresh materialized view)
            success_count = await self._store_performance_snapshots(all_metrics)

            log_info(
                f"Daily snapshot calculation completed. Processed {success_count} schemes"
            )
            return success_count

        except Exception as e:
            log_error(f"Error in daily snapshot calculation: {e}")
            return 0

    async def _store_performance_snapshots(
        self, metrics_list: List[PerformanceMetrics]
    ) -> int:
        """
        Store performance snapshots to TimescaleDB hypertable.

        Args:
            metrics_list: List of calculated metrics

        Returns:
            Number of records stored successfully
        """
        try:
            success_count = 0

            for metrics in metrics_list:
                try:
                    # Insert or update performance snapshot
                    query = text(
                        """
                        INSERT INTO performance_snapshots (
                            schemecode, date, fund_name, aum, asset_class, investment_objective,
                            nav, perf_1w, perf_1m, perf_3m, perf_6m, perf_1y,
                            absret_1y, absret_3m, absret_6m,
                            cagr_3y, cagr_5y, cagr_10y, rolling_3y_avg_ret,
                            ret_vs_subcat_1y, ret_vs_subcat_3y, ret_vs_subcat_5y, ret_vs_subcat_10y,
                            percent_from_ath, percent_from_atl, created_at, updated_at, flag
                        ) VALUES (
                            :schemecode, :date, :fund_name, :aum, :asset_class, :investment_objective,
                            :nav, :perf_1w, :perf_1m, :perf_3m, :perf_6m, :perf_1y,
                            :absret_1y, :absret_3m, :absret_6m,
                            :cagr_3y, :cagr_5y, :cagr_10y, :rolling_3y_avg_ret,
                            :ret_vs_subcat_1y, :ret_vs_subcat_3y, :ret_vs_subcat_5y, :ret_vs_subcat_10y,
                            :percent_from_ath, :percent_from_atl, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'A'
                        )
                        ON CONFLICT (schemecode, date)
                        DO UPDATE SET
                            fund_name = EXCLUDED.fund_name,
                            aum = EXCLUDED.aum,
                            asset_class = EXCLUDED.asset_class,
                            investment_objective = EXCLUDED.investment_objective,
                            nav = EXCLUDED.nav,
                            perf_1w = EXCLUDED.perf_1w,
                            perf_1m = EXCLUDED.perf_1m,
                            perf_3m = EXCLUDED.perf_3m,
                            perf_6m = EXCLUDED.perf_6m,
                            perf_1y = EXCLUDED.perf_1y,
                            absret_1y = EXCLUDED.absret_1y,
                            absret_3m = EXCLUDED.absret_3m,
                            absret_6m = EXCLUDED.absret_6m,
                            cagr_3y = EXCLUDED.cagr_3y,
                            cagr_5y = EXCLUDED.cagr_5y,
                            cagr_10y = EXCLUDED.cagr_10y,
                            rolling_3y_avg_ret = EXCLUDED.rolling_3y_avg_ret,
                            ret_vs_subcat_1y = EXCLUDED.ret_vs_subcat_1y,
                            ret_vs_subcat_3y = EXCLUDED.ret_vs_subcat_3y,
                            ret_vs_subcat_5y = EXCLUDED.ret_vs_subcat_5y,
                            ret_vs_subcat_10y = EXCLUDED.ret_vs_subcat_10y,
                            percent_from_ath = EXCLUDED.percent_from_ath,
                            percent_from_atl = EXCLUDED.percent_from_atl,
                            updated_at = CURRENT_TIMESTAMP
                    """
                    )

                    await self.session.execute(
                        query,
                        {
                            "schemecode": metrics.schemecode,
                            "date": metrics.calculation_date,
                            "fund_name": metrics.fund_name,
                            "aum": metrics.aum,
                            "asset_class": metrics.asset_class,
                            "investment_objective": metrics.investment_objective,
                            "nav": metrics.nav,
                            "perf_1w": metrics.perf_1w,
                            "perf_1m": metrics.perf_1m,
                            "perf_3m": metrics.perf_3m,
                            "perf_6m": metrics.perf_6m,
                            "perf_1y": metrics.perf_1y,
                            "absret_1y": metrics.absret_1y,
                            "absret_3m": metrics.absret_3m,
                            "absret_6m": metrics.absret_6m,
                            "cagr_3y": metrics.cagr_3y,
                            "cagr_5y": metrics.cagr_5y,
                            "cagr_10y": metrics.cagr_10y,
                            "rolling_3y_avg_ret": metrics.rolling_3y_avg_ret,
                            "ret_vs_subcat_1y": metrics.ret_vs_subcat_1y,
                            "ret_vs_subcat_3y": metrics.ret_vs_subcat_3y,
                            "ret_vs_subcat_5y": metrics.ret_vs_subcat_5y,
                            "ret_vs_subcat_10y": metrics.ret_vs_subcat_10y,
                            "percent_from_ath": metrics.percent_from_ath,
                            "percent_from_atl": metrics.percent_from_atl,
                        },
                    )

                    success_count += 1

                except Exception as e:
                    log_error(
                        f"Error storing metrics for scheme {metrics.schemecode}: {e}"
                    )

            await self.session.commit()
            log_info(
                f"Stored {success_count} performance snapshots to TimescaleDB hypertable"
            )
            return success_count

        except Exception as e:
            log_error(f"Error storing performance snapshots: {e}")
            await self.session.rollback()
            return 0

    async def get_calculation_summary(
        self, calculation_date: Optional[date] = None
    ) -> Dict[str, int]:
        """
        Get summary of calculation coverage.

        Args:
            calculation_date: Date to check (default: today)

        Returns:
            Dictionary with calculation statistics
        """
        if calculation_date is None:
            calculation_date = date.today()

        try:
            query = text(
                """
                SELECT
                    COUNT(*) as total_schemes,
                    COUNT(CASE WHEN perf_1y IS NOT NULL THEN 1 END) as schemes_with_1y,
                    COUNT(CASE WHEN cagr_3y IS NOT NULL THEN 1 END) as schemes_with_3y,
                    COUNT(CASE WHEN cagr_5y IS NOT NULL THEN 1 END) as schemes_with_5y,
                    COUNT(CASE WHEN cagr_10y IS NOT NULL THEN 1 END) as schemes_with_10y
                FROM performance_snapshots
                WHERE date = :calculation_date
            """
            )

            result = await self.session.execute(
                query, {"calculation_date": calculation_date}
            )
            row = result.fetchone()

            if row:
                return {
                    "total_schemes": row[0] or 0,
                    "schemes_with_1y_data": row[1] or 0,
                    "schemes_with_3y_data": row[2] or 0,
                    "schemes_with_5y_data": row[3] or 0,
                    "schemes_with_10y_data": row[4] or 0,
                }

            return {
                "total_schemes": 0,
                "schemes_with_1y_data": 0,
                "schemes_with_3y_data": 0,
                "schemes_with_5y_data": 0,
                "schemes_with_10y_data": 0,
            }

        except Exception as e:
            log_error(f"Error getting calculation summary: {e}")
            return {
                "total_schemes": 0,
                "schemes_with_1y_data": 0,
                "schemes_with_3y_data": 0,
                "schemes_with_5y_data": 0,
                "schemes_with_10y_data": 0,
            }
