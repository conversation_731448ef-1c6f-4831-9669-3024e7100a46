"""Performance calculation engine for mutual fund metrics."""

from datetime import date, timedelta
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional, Dict, List, Tuple
from dataclasses import dataclass
import math

from sqlalchemy.ext.asyncio import AsyncSession
from config.logger import log_info, log_error, log_warning

from .nav_utils import NAVRetriever, get_performance_nav_points
from .trading_calendar import TradingCalendar, get_performance_calculation_dates


@dataclass
class PerformanceMetrics:
    """Data class to hold all calculated performance metrics."""

    # Identification
    schemecode: int
    calculation_date: date

    # Metadata
    fund_name: Optional[str] = None
    asset_class: Optional[str] = None
    investment_objective: Optional[str] = None
    aum: Optional[Decimal] = None

    # Raw NAV
    nav: Optional[Decimal] = None

    # Short-term returns
    perf_1w: Optional[Decimal] = None
    perf_1m: Optional[Decimal] = None

    # Medium-term returns
    perf_3m: Optional[Decimal] = None
    perf_6m: Optional[Decimal] = None
    perf_1y: Optional[Decimal] = None

    # Absolute returns
    absret_1y: Optional[Decimal] = None
    absret_3m: Optional[Decimal] = None
    absret_6m: Optional[Decimal] = None

    # CAGR metrics
    cagr_3y: Optional[Decimal] = None
    cagr_5y: Optional[Decimal] = None
    cagr_10y: Optional[Decimal] = None

    # Rolling returns
    rolling_3y_avg_ret: Optional[Decimal] = None

    # Relative performance
    ret_vs_subcat_1y: Optional[Decimal] = None
    ret_vs_subcat_3y: Optional[Decimal] = None
    ret_vs_subcat_5y: Optional[Decimal] = None
    ret_vs_subcat_10y: Optional[Decimal] = None

    # ATH/ATL metrics
    percent_from_ath: Optional[Decimal] = None
    percent_from_atl: Optional[Decimal] = None


class PerformanceCalculator:
    """Core engine for calculating mutual fund performance metrics."""

    def __init__(self, session: AsyncSession):
        self.session = session
        self.nav_retriever = NAVRetriever(session)

    def _safe_divide(
        self, numerator: Optional[Decimal], denominator: Optional[Decimal]
    ) -> Optional[Decimal]:
        """Safely divide two decimals, returning None if invalid."""
        if not numerator or not denominator or denominator == 0:
            return None
        try:
            return numerator / denominator
        except (ValueError, TypeError, ZeroDivisionError):
            return None

    def _safe_power(
        self, base: Optional[Decimal], exponent: float
    ) -> Optional[Decimal]:
        """Safely calculate power, returning None if invalid."""
        if not base or base <= 0:
            return None
        try:
            # Convert to float for power calculation, then back to Decimal
            result = float(base) ** exponent
            return Decimal(str(result)).quantize(
                Decimal("0.000001"), rounding=ROUND_HALF_UP
            )
        except (ValueError, TypeError, OverflowError):
            return None

    def calculate_simple_return(
        self, current_nav: Optional[Decimal], previous_nav: Optional[Decimal]
    ) -> Optional[Decimal]:
        """
        Calculate simple return percentage.

        Formula: ((current_nav - previous_nav) / previous_nav) * 100
        """
        if not current_nav or not previous_nav or previous_nav == 0:
            return None

        try:
            return_decimal = ((current_nav - previous_nav) / previous_nav) * 100
            return return_decimal.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
        except (ValueError, TypeError, ZeroDivisionError):
            return None

    def calculate_cagr(
        self,
        current_nav: Optional[Decimal],
        initial_nav: Optional[Decimal],
        years: float,
    ) -> Optional[Decimal]:
        """
        Calculate Compound Annual Growth Rate.

        Formula: ((current_nav / initial_nav) ^ (1/years) - 1) * 100
        """
        if not current_nav or not initial_nav or initial_nav == 0 or years <= 0:
            return None

        try:
            ratio = current_nav / initial_nav
            cagr = (self._safe_power(ratio, 1.0 / years) or Decimal("1")) - Decimal("1")
            return (cagr * 100).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
        except (ValueError, TypeError):
            return None

    def calculate_ath_atl_percentages(
        self,
        current_nav: Optional[Decimal],
        ath: Optional[Decimal],
        atl: Optional[Decimal],
    ) -> Tuple[Optional[Decimal], Optional[Decimal]]:
        """
        Calculate percentage from all-time high and all-time low.

        Returns: (percent_from_ath, percent_from_atl)
        """
        percent_from_ath = None
        percent_from_atl = None

        if current_nav and ath and ath > 0:
            percent_from_ath = ((current_nav / ath - 1) * 100).quantize(
                Decimal("0.01"), rounding=ROUND_HALF_UP
            )

        if current_nav and atl and atl > 0:
            percent_from_atl = ((current_nav / atl - 1) * 100).quantize(
                Decimal("0.01"), rounding=ROUND_HALF_UP
            )

        return percent_from_ath, percent_from_atl

    async def get_scheme_metadata(self, schemecode: int) -> Dict[str, Optional[str]]:
        """Get scheme metadata from database."""
        try:
            from sqlalchemy import text

            query = text(
                """
                SELECT 
                    sm.scheme_name,
                    sc.asset_type,
                    sc.category,
                    COALESCE(sa.total, 0) as aum
                FROM scheme_master sm
                LEFT JOIN scheme_details sd ON sm.schemecode = sd.schemecode
                LEFT JOIN sclass_mst sc ON sd.classcode = sc.classcode
                LEFT JOIN (
                    SELECT DISTINCT ON (schemecode) 
                        schemecode, total
                    FROM scheme_aum 
                    WHERE date <= CURRENT_DATE
                    ORDER BY schemecode, date DESC
                ) sa ON sm.schemecode = sa.schemecode
                WHERE sm.schemecode = :schemecode
            """
            )

            result = await self.session.execute(query, {"schemecode": schemecode})
            row = result.fetchone()

            if row:
                return {
                    "fund_name": row[0],
                    "asset_class": row[1],
                    "investment_objective": row[2],
                    "aum": Decimal(str(row[3])) if row[3] else None,
                }

            return {
                "fund_name": None,
                "asset_class": None,
                "investment_objective": None,
                "aum": None,
            }

        except Exception as e:
            log_error(f"Error getting metadata for scheme {schemecode}: {e}")
            return {
                "fund_name": None,
                "asset_class": None,
                "investment_objective": None,
                "aum": None,
            }

    async def calculate_rolling_3y_average(
        self, schemecode: int, end_date: date, window_months: int = 36
    ) -> Optional[Decimal]:
        """
        Calculate rolling 3-year average return.

        This is a simplified version - can be enhanced for true rolling windows.
        """
        try:
            # For now, use the 3Y CAGR as a proxy
            # In a full implementation, this would calculate multiple overlapping 3Y periods
            start_date = end_date - timedelta(days=window_months * 30)

            current_nav_data = await self.nav_retriever.get_latest_nav(schemecode)
            if not current_nav_data:
                return None

            historical_nav = await self.nav_retriever.get_nav_safe(
                schemecode, start_date
            )
            if not historical_nav:
                return None

            return self.calculate_cagr(current_nav_data[1], historical_nav, 3.0)

        except Exception as e:
            log_error(
                f"Error calculating rolling 3Y average for scheme {schemecode}: {e}"
            )
            return None

    async def calculate_all_metrics(
        self, schemecode: int, calculation_date: Optional[date] = None
    ) -> PerformanceMetrics:
        """
        Calculate all performance metrics for a scheme.

        Args:
            schemecode: Unique scheme identifier
            calculation_date: Date for calculations (default: today)

        Returns:
            PerformanceMetrics object with all calculated values
        """
        if calculation_date is None:
            calculation_date = date.today()

        log_info(
            f"Calculating performance metrics for scheme {schemecode} on {calculation_date}"
        )

        # Initialize metrics object
        metrics = PerformanceMetrics(
            schemecode=schemecode, calculation_date=calculation_date
        )

        try:
            # Get scheme metadata
            metadata = await self.get_scheme_metadata(schemecode)
            metrics.fund_name = metadata["fund_name"]
            metrics.asset_class = metadata["asset_class"]
            metrics.investment_objective = metadata["investment_objective"]
            metrics.aum = metadata["aum"]

            # Get all required NAV points
            nav_points = await get_performance_nav_points(
                self.session, schemecode, calculation_date
            )

            current_nav = nav_points.get("current")
            metrics.nav = current_nav

            if not current_nav:
                log_warning(f"No current NAV found for scheme {schemecode}")
                return metrics

            # Calculate simple returns
            metrics.perf_1w = self.calculate_simple_return(
                current_nav, nav_points.get("1w")
            )
            metrics.perf_1m = self.calculate_simple_return(
                current_nav, nav_points.get("1m")
            )
            metrics.perf_3m = self.calculate_simple_return(
                current_nav, nav_points.get("3m")
            )
            metrics.perf_6m = self.calculate_simple_return(
                current_nav, nav_points.get("6m")
            )
            metrics.perf_1y = self.calculate_simple_return(
                current_nav, nav_points.get("1y")
            )

            # Absolute returns (same as simple returns for now)
            metrics.absret_3m = metrics.perf_3m
            metrics.absret_6m = metrics.perf_6m
            metrics.absret_1y = metrics.perf_1y

            # Calculate CAGR metrics
            metrics.cagr_3y = self.calculate_cagr(
                current_nav, nav_points.get("3y"), 3.0
            )
            metrics.cagr_5y = self.calculate_cagr(
                current_nav, nav_points.get("5y"), 5.0
            )
            metrics.cagr_10y = self.calculate_cagr(
                current_nav, nav_points.get("10y"), 10.0
            )

            # Calculate rolling 3Y average
            metrics.rolling_3y_avg_ret = await self.calculate_rolling_3y_average(
                schemecode, calculation_date
            )

            # Calculate ATH/ATL percentages
            ath, atl = await self.nav_retriever.get_ath_atl(
                schemecode, calculation_date
            )
            metrics.percent_from_ath, metrics.percent_from_atl = (
                self.calculate_ath_atl_percentages(current_nav, ath, atl)
            )

            # Note: Relative performance vs sub-category will be calculated separately
            # as it requires aggregating data across multiple schemes

            log_info(f"Successfully calculated metrics for scheme {schemecode}")
            return metrics

        except Exception as e:
            log_error(f"Error calculating metrics for scheme {schemecode}: {e}")
            return metrics

    async def calculate_subcategory_averages(
        self, asset_class: str, calculation_date: date
    ) -> Dict[str, Optional[Decimal]]:
        """
        Calculate average performance metrics for a sub-category.

        Args:
            asset_class: Asset class to calculate averages for
            calculation_date: Date for calculations

        Returns:
            Dictionary with average metrics
        """
        try:
            from sqlalchemy import text

            # Query the TimescaleDB continuous aggregate for real-time category averages
            query = text(
                """
                SELECT
                    avg_perf_1y,
                    avg_cagr_3y,
                    avg_cagr_5y,
                    avg_cagr_10y
                FROM performance_category_averages
                WHERE asset_class = :asset_class
                  AND day = :calculation_date
            """
            )

            result = await self.session.execute(
                query,
                {"asset_class": asset_class, "calculation_date": calculation_date},
            )

            row = result.fetchone()
            if row:
                return {
                    "avg_perf_1y": Decimal(str(row[0])) if row[0] else None,
                    "avg_cagr_3y": Decimal(str(row[1])) if row[1] else None,
                    "avg_cagr_5y": Decimal(str(row[2])) if row[2] else None,
                    "avg_cagr_10y": Decimal(str(row[3])) if row[3] else None,
                }

            return {
                "avg_perf_1y": None,
                "avg_cagr_3y": None,
                "avg_cagr_5y": None,
                "avg_cagr_10y": None,
            }

        except Exception as e:
            log_error(f"Error calculating subcategory averages for {asset_class}: {e}")
            return {
                "avg_perf_1y": None,
                "avg_cagr_3y": None,
                "avg_cagr_5y": None,
                "avg_cagr_10y": None,
            }

    async def add_relative_performance(
        self, metrics: PerformanceMetrics
    ) -> PerformanceMetrics:
        """
        Add relative performance metrics to existing PerformanceMetrics.

        Args:
            metrics: PerformanceMetrics object to enhance

        Returns:
            Enhanced PerformanceMetrics with relative performance
        """
        if not metrics.asset_class:
            return metrics

        try:
            subcategory_avgs = await self.calculate_subcategory_averages(
                metrics.asset_class, metrics.calculation_date
            )

            # Calculate relative performance
            if metrics.perf_1y and subcategory_avgs["avg_perf_1y"]:
                metrics.ret_vs_subcat_1y = (
                    metrics.perf_1y - subcategory_avgs["avg_perf_1y"]
                ).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

            if metrics.cagr_3y and subcategory_avgs["avg_cagr_3y"]:
                metrics.ret_vs_subcat_3y = (
                    metrics.cagr_3y - subcategory_avgs["avg_cagr_3y"]
                ).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

            if metrics.cagr_5y and subcategory_avgs["avg_cagr_5y"]:
                metrics.ret_vs_subcat_5y = (
                    metrics.cagr_5y - subcategory_avgs["avg_cagr_5y"]
                ).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

            if metrics.cagr_10y and subcategory_avgs["avg_cagr_10y"]:
                metrics.ret_vs_subcat_10y = (
                    metrics.cagr_10y - subcategory_avgs["avg_cagr_10y"]
                ).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

            return metrics

        except Exception as e:
            log_error(
                f"Error adding relative performance for scheme {metrics.schemecode}: {e}"
            )
            return metrics
