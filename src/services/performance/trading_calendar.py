"""Trading calendar utilities for Indian stock market holidays."""

from datetime import date, datetime, timedelta
from typing import List, Set
import pytz
from utils.datetime_support import asia_kolkata


class TradingCalendar:
    """Utility class for handling Indian stock market trading days and holidays."""
    
    # Indian stock market holidays (this should be updated annually)
    # These are common holidays - actual list should be maintained from NSE/BSE
    FIXED_HOLIDAYS = {
        # Republic Day
        (1, 26),
        # Independence Day  
        (8, 15),
        # <PERSON>
        (10, 2),
    }
    
    # Variable holidays by year (these change annually)
    VARIABLE_HOLIDAYS_2024 = {
        date(2024, 1, 22),  # Ram <PERSON> Prat<PERSON>tha
        date(2024, 3, 8),   # Holi
        date(2024, 3, 25),  # Holi (second day)
        date(2024, 3, 29),  # Good Friday
        date(2024, 4, 11),  # Eid ul-Fitr
        date(2024, 4, 14),  # <PERSON><PERSON> <PERSON><PERSON>
        date(2024, 4, 17),  # Ram <PERSON>mi
        date(2024, 5, 1),   # Maharashtra Day
        date(2024, 6, 17),  # <PERSON><PERSON> ul<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)
        date(2024, 8, 19),  # <PERSON><PERSON><PERSON>
        date(2024, 8, 26),  # <PERSON><PERSON><PERSON>i
        date(2024, 9, 7),   # <PERSON><PERSON><PERSON>
        date(2024, 10, 12), # <PERSON>sse<PERSON>
        date(2024, 11, 1),  # Diwali (Laxmi Pujan)
        date(2024, 11, 2),  # Diwali Balipratipada
        date(2024, 11, 15), # <PERSON> Nanak <PERSON>nti
    }
    
    VARIABLE_HOLIDAYS_2025 = {
        date(2025, 2, 26),  # Holi
        date(2025, 3, 14),  # Holi (second day)
        date(2025, 3, 31),  # Eid ul-Fitr
        date(2025, 4, 6),   # Ram Navami
        date(2025, 4, 14),  # Dr. Ambedkar Jayanti
        date(2025, 4, 18),  # Good Friday
        date(2025, 5, 1),   # Maharashtra Day
        date(2025, 6, 6),   # Eid ul-Adha (Bakri Id)
        date(2025, 8, 9),   # Raksha Bandhan
        date(2025, 8, 16),  # Janmashtami
        date(2025, 8, 27),  # Ganesh Chaturthi
        date(2025, 10, 2),  # Dussehra
        date(2025, 10, 20), # Diwali (Laxmi Pujan)
        date(2025, 10, 21), # Diwali Balipratipada
        date(2025, 11, 5),  # Guru Nanak Jayanti
    }
    
    @classmethod
    def is_weekend(cls, check_date: date) -> bool:
        """Check if a date falls on weekend (Saturday=5, Sunday=6)."""
        return check_date.weekday() >= 5
    
    @classmethod
    def is_fixed_holiday(cls, check_date: date) -> bool:
        """Check if a date is a fixed holiday."""
        return (check_date.month, check_date.day) in cls.FIXED_HOLIDAYS
    
    @classmethod
    def is_variable_holiday(cls, check_date: date) -> bool:
        """Check if a date is a variable holiday."""
        year = check_date.year
        
        if year == 2024:
            return check_date in cls.VARIABLE_HOLIDAYS_2024
        elif year == 2025:
            return check_date in cls.VARIABLE_HOLIDAYS_2025
        
        # For other years, return False (should be updated with actual data)
        return False
    
    @classmethod
    def is_trading_day(cls, check_date: date) -> bool:
        """
        Check if a date is a trading day (not weekend or holiday).
        
        Args:
            check_date: Date to check
            
        Returns:
            True if it's a trading day, False otherwise
        """
        return not (
            cls.is_weekend(check_date) or
            cls.is_fixed_holiday(check_date) or
            cls.is_variable_holiday(check_date)
        )
    
    @classmethod
    def get_previous_trading_day(cls, from_date: date, max_lookback: int = 10) -> date:
        """
        Get the most recent trading day on or before the given date.
        
        Args:
            from_date: Starting date
            max_lookback: Maximum days to look back
            
        Returns:
            Most recent trading day
        """
        current_date = from_date
        days_checked = 0
        
        while days_checked < max_lookback:
            if cls.is_trading_day(current_date):
                return current_date
            
            current_date -= timedelta(days=1)
            days_checked += 1
        
        # If no trading day found, return the original date
        return from_date
    
    @classmethod
    def get_next_trading_day(cls, from_date: date, max_lookforward: int = 10) -> date:
        """
        Get the next trading day on or after the given date.
        
        Args:
            from_date: Starting date
            max_lookforward: Maximum days to look forward
            
        Returns:
            Next trading day
        """
        current_date = from_date
        days_checked = 0
        
        while days_checked < max_lookforward:
            if cls.is_trading_day(current_date):
                return current_date
            
            current_date += timedelta(days=1)
            days_checked += 1
        
        # If no trading day found, return the original date
        return from_date
    
    @classmethod
    def get_trading_days_between(cls, start_date: date, end_date: date) -> List[date]:
        """
        Get all trading days between two dates (inclusive).
        
        Args:
            start_date: Start date
            end_date: End date
            
        Returns:
            List of trading days
        """
        trading_days = []
        current_date = start_date
        
        while current_date <= end_date:
            if cls.is_trading_day(current_date):
                trading_days.append(current_date)
            current_date += timedelta(days=1)
        
        return trading_days
    
    @classmethod
    def count_trading_days(cls, start_date: date, end_date: date) -> int:
        """
        Count trading days between two dates (inclusive).
        
        Args:
            start_date: Start date
            end_date: End date
            
        Returns:
            Number of trading days
        """
        return len(cls.get_trading_days_between(start_date, end_date))
    
    @classmethod
    def adjust_date_for_trading(cls, target_date: date, direction: str = "backward") -> date:
        """
        Adjust a date to the nearest trading day.
        
        Args:
            target_date: Date to adjust
            direction: "backward" (default) or "forward"
            
        Returns:
            Adjusted trading day
        """
        if cls.is_trading_day(target_date):
            return target_date
        
        if direction == "backward":
            return cls.get_previous_trading_day(target_date)
        else:
            return cls.get_next_trading_day(target_date)
    
    @classmethod
    def get_market_time_now(cls) -> datetime:
        """Get current time in Indian market timezone."""
        return datetime.now(asia_kolkata)
    
    @classmethod
    def is_market_hours(cls, check_time: datetime = None) -> bool:
        """
        Check if current time is within market hours (9:15 AM to 3:30 PM IST).
        
        Args:
            check_time: Time to check (default: current time)
            
        Returns:
            True if within market hours, False otherwise
        """
        if check_time is None:
            check_time = cls.get_market_time_now()
        
        # Convert to IST if not already
        if check_time.tzinfo != asia_kolkata:
            check_time = check_time.astimezone(asia_kolkata)
        
        # Check if it's a trading day
        if not cls.is_trading_day(check_time.date()):
            return False
        
        # Market hours: 9:15 AM to 3:30 PM
        market_open = check_time.replace(hour=9, minute=15, second=0, microsecond=0)
        market_close = check_time.replace(hour=15, minute=30, second=0, microsecond=0)
        
        return market_open <= check_time <= market_close


# Utility functions for direct use
def get_safe_nav_date(target_date: date) -> date:
    """Get a safe date for NAV lookup (previous trading day if needed)."""
    return TradingCalendar.get_previous_trading_day(target_date)


def is_nav_available_date(check_date: date) -> bool:
    """Check if NAV should be available for a given date."""
    return TradingCalendar.is_trading_day(check_date)


def get_performance_calculation_dates(base_date: date = None) -> dict[str, date]:
    """
    Get all the dates needed for performance calculations, adjusted for trading days.
    
    Args:
        base_date: Base date for calculations (default: today)
        
    Returns:
        Dictionary with period names and their corresponding safe dates
    """
    if base_date is None:
        base_date = date.today()
    
    # Ensure base date is a trading day
    safe_base_date = TradingCalendar.get_previous_trading_day(base_date)
    
    periods = {
        "1w": safe_base_date - timedelta(days=7),
        "1m": safe_base_date - timedelta(days=30),
        "3m": safe_base_date - timedelta(days=90),
        "6m": safe_base_date - timedelta(days=180),
        "1y": safe_base_date - timedelta(days=365),
        "3y": safe_base_date - timedelta(days=1095),
        "5y": safe_base_date - timedelta(days=1825),
        "10y": safe_base_date - timedelta(days=3650),
    }
    
    # Adjust each period date to the nearest previous trading day
    safe_periods = {}
    for period, target_date in periods.items():
        safe_periods[period] = TradingCalendar.get_previous_trading_day(target_date)
    
    safe_periods["current"] = safe_base_date
    return safe_periods
