"""NAV utility functions with holiday-safe fallback logic."""

from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import Optional, List, Tuple
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from config.logger import log_info, log_error


class NAVRetriever:
    """Utility class for holiday-safe NAV retrieval operations."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def get_nav_safe(
        self, 
        schemecode: int, 
        target_date: date,
        max_lookback_days: int = 30
    ) -> Optional[Decimal]:
        """
        Get NAV for a scheme on or before the target date with holiday-safe fallback.
        
        Args:
            schemecode: Unique scheme identifier
            target_date: Target date for NAV lookup
            max_lookback_days: Maximum days to look back for NAV (default: 30)
            
        Returns:
            NAV value or None if not found within lookback period
        """
        try:
            query = text("""
                SELECT navrs
                FROM navhist
                WHERE schemecode = :schemecode 
                  AND navdate <= :target_date
                  AND navdate >= :min_date
                  AND navrs > 0
                ORDER BY navdate DESC
                LIMIT 1
            """)
            
            min_date = target_date - timedelta(days=max_lookback_days)
            
            result = await self.session.execute(
                query,
                {
                    "schemecode": schemecode,
                    "target_date": target_date,
                    "min_date": min_date
                }
            )
            
            row = result.fetchone()
            return Decimal(str(row[0])) if row and row[0] else None
            
        except Exception as e:
            log_error(f"Error retrieving NAV for scheme {schemecode} on {target_date}: {e}")
            return None
    
    async def get_nav_range(
        self,
        schemecode: int,
        start_date: date,
        end_date: date
    ) -> List[Tuple[date, Decimal]]:
        """
        Get all NAV values for a scheme within a date range.
        
        Args:
            schemecode: Unique scheme identifier
            start_date: Start date for range
            end_date: End date for range
            
        Returns:
            List of (date, nav) tuples ordered by date
        """
        try:
            query = text("""
                SELECT navdate, navrs
                FROM navhist
                WHERE schemecode = :schemecode 
                  AND navdate BETWEEN :start_date AND :end_date
                  AND navrs > 0
                ORDER BY navdate ASC
            """)
            
            result = await self.session.execute(
                query,
                {
                    "schemecode": schemecode,
                    "start_date": start_date,
                    "end_date": end_date
                }
            )
            
            return [
                (row[0], Decimal(str(row[1])))
                for row in result.fetchall()
                if row[1] is not None
            ]
            
        except Exception as e:
            log_error(f"Error retrieving NAV range for scheme {schemecode}: {e}")
            return []
    
    async def get_latest_nav(self, schemecode: int) -> Optional[Tuple[date, Decimal]]:
        """
        Get the most recent NAV for a scheme.
        
        Args:
            schemecode: Unique scheme identifier
            
        Returns:
            Tuple of (date, nav) or None if not found
        """
        try:
            query = text("""
                SELECT navdate, navrs
                FROM navhist
                WHERE schemecode = :schemecode 
                  AND navrs > 0
                ORDER BY navdate DESC
                LIMIT 1
            """)
            
            result = await self.session.execute(
                query,
                {"schemecode": schemecode}
            )
            
            row = result.fetchone()
            if row and row[0] and row[1]:
                return (row[0], Decimal(str(row[1])))
            return None
            
        except Exception as e:
            log_error(f"Error retrieving latest NAV for scheme {schemecode}: {e}")
            return None
    
    async def get_nav_at_intervals(
        self,
        schemecode: int,
        base_date: date,
        intervals_days: List[int]
    ) -> dict[int, Optional[Decimal]]:
        """
        Get NAV values at multiple intervals before a base date.
        
        Args:
            schemecode: Unique scheme identifier
            base_date: Base date to calculate intervals from
            intervals_days: List of days to look back (e.g., [7, 30, 90, 365])
            
        Returns:
            Dictionary mapping interval days to NAV values
        """
        results = {}
        
        for days in intervals_days:
            target_date = base_date - timedelta(days=days)
            nav = await self.get_nav_safe(schemecode, target_date)
            results[days] = nav
            
        return results
    
    async def get_ath_atl(
        self,
        schemecode: int,
        up_to_date: Optional[date] = None
    ) -> Tuple[Optional[Decimal], Optional[Decimal]]:
        """
        Get all-time high and all-time low NAV for a scheme.
        
        Args:
            schemecode: Unique scheme identifier
            up_to_date: Calculate ATH/ATL up to this date (default: no limit)
            
        Returns:
            Tuple of (all_time_high, all_time_low) or (None, None) if not found
        """
        try:
            date_filter = ""
            params = {"schemecode": schemecode}
            
            if up_to_date:
                date_filter = "AND navdate <= :up_to_date"
                params["up_to_date"] = up_to_date
            
            query = text(f"""
                SELECT 
                    MAX(navrs) as ath,
                    MIN(navrs) as atl
                FROM navhist
                WHERE schemecode = :schemecode 
                  AND navrs > 0
                  {date_filter}
            """)
            
            result = await self.session.execute(query, params)
            row = result.fetchone()
            
            if row:
                ath = Decimal(str(row[0])) if row[0] else None
                atl = Decimal(str(row[1])) if row[1] else None
                return (ath, atl)
            
            return (None, None)
            
        except Exception as e:
            log_error(f"Error retrieving ATH/ATL for scheme {schemecode}: {e}")
            return (None, None)
    
    async def validate_nav_availability(
        self,
        schemecode: int,
        required_periods_days: List[int],
        base_date: Optional[date] = None
    ) -> dict[int, bool]:
        """
        Check if NAV data is available for required time periods.
        
        Args:
            schemecode: Unique scheme identifier
            required_periods_days: List of required periods in days
            base_date: Base date for calculations (default: today)
            
        Returns:
            Dictionary mapping period days to availability (True/False)
        """
        if base_date is None:
            base_date = date.today()
        
        availability = {}
        
        for days in required_periods_days:
            target_date = base_date - timedelta(days=days)
            nav = await self.get_nav_safe(schemecode, target_date, max_lookback_days=7)
            availability[days] = nav is not None
            
        return availability


# Utility functions for direct use
async def get_nav_safe_simple(
    session: AsyncSession,
    schemecode: int,
    target_date: date
) -> Optional[Decimal]:
    """Simple wrapper for holiday-safe NAV retrieval."""
    retriever = NAVRetriever(session)
    return await retriever.get_nav_safe(schemecode, target_date)


async def get_performance_nav_points(
    session: AsyncSession,
    schemecode: int,
    base_date: Optional[date] = None
) -> dict[str, Optional[Decimal]]:
    """
    Get all NAV points needed for standard performance calculations.
    
    Returns:
        Dictionary with keys: current, 1w, 1m, 3m, 6m, 1y, 3y, 5y, 10y
    """
    if base_date is None:
        base_date = date.today()
    
    retriever = NAVRetriever(session)
    
    # Get current NAV
    current_nav_data = await retriever.get_latest_nav(schemecode)
    current_nav = current_nav_data[1] if current_nav_data else None
    
    # Get historical NAVs
    intervals = [7, 30, 90, 180, 365, 1095, 1825, 3650]  # 1w, 1m, 3m, 6m, 1y, 3y, 5y, 10y
    historical_navs = await retriever.get_nav_at_intervals(schemecode, base_date, intervals)
    
    return {
        "current": current_nav,
        "1w": historical_navs.get(7),
        "1m": historical_navs.get(30),
        "3m": historical_navs.get(90),
        "6m": historical_navs.get(180),
        "1y": historical_navs.get(365),
        "3y": historical_navs.get(1095),
        "5y": historical_navs.get(1825),
        "10y": historical_navs.get(3650),
    }
