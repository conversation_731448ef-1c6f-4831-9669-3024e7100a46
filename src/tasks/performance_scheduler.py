"""Performance calculation task scheduler configuration."""

from datetime import datetime, time
from celery.schedules import crontab
from utils.datetime_support import asia_kolkata

# Performance calculation schedule configuration
PERFORMANCE_SCHEDULE = {
    # Daily performance snapshot - runs at 10:30 PM IST after NAV updates
    "daily-performance-snapshot": {
        "task": "performance.daily_snapshot",
        "schedule": crontab(hour=22, minute=30, timezone=asia_kolkata),
        "kwargs": {"force_refresh": False},
        "options": {
            "expires": 3600,  # Task expires after 1 hour
            "retry": True,
            "retry_policy": {
                "max_retries": 3,
                "interval_start": 300,  # 5 minutes
                "interval_step": 300,  # 5 minutes
                "interval_max": 900,  # 15 minutes
            },
        },
    },
    # Note: TimescaleDB continuous aggregates refresh automatically
    # No manual refresh tasks needed
}


def get_performance_beat_schedule():
    """
    Get the performance calculation beat schedule for Celery.

    This function can be imported in your main Celery configuration.

    Returns:
        Dictionary with beat schedule configuration
    """
    return PERFORMANCE_SCHEDULE


def schedule_backfill_task(
    start_date: str, end_date: str, asset_class_filter: str = None
):
    """
    Schedule a backfill task for a date range.

    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        asset_class_filter: Optional asset class filter

    Returns:
        Celery task result
    """
    from src.tasks.performance_snapshot_worker import backfill_performance_snapshots

    return backfill_performance_snapshots.delay(
        start_date=start_date, end_date=end_date, asset_class_filter=asset_class_filter
    )


def schedule_immediate_snapshot(
    calculation_date: str = None, force_refresh: bool = True
):
    """
    Schedule an immediate performance snapshot calculation.

    Args:
        calculation_date: Date for calculations in YYYY-MM-DD format (default: today)
        force_refresh: Force refresh even if data exists

    Returns:
        Celery task result
    """
    from src.tasks.performance_snapshot_worker import daily_performance_snapshot

    return daily_performance_snapshot.delay(
        calculation_date=calculation_date, force_refresh=force_refresh
    )


# Note: TimescaleDB continuous aggregates refresh automatically
# No manual refresh scheduling needed


# Task monitoring and management utilities
class PerformanceTaskMonitor:
    """Utility class for monitoring performance calculation tasks."""

    @staticmethod
    def get_task_status(task_id: str):
        """Get status of a performance calculation task."""
        from src.tasks.worker import celery

        result = celery.AsyncResult(task_id)
        return {
            "task_id": task_id,
            "status": result.status,
            "result": result.result if result.ready() else None,
            "traceback": result.traceback if result.failed() else None,
            "date_done": result.date_done.isoformat() if result.date_done else None,
        }

    @staticmethod
    def get_active_performance_tasks():
        """Get list of active performance calculation tasks."""
        from src.tasks.worker import celery

        inspect = celery.control.inspect()
        active_tasks = inspect.active()

        performance_tasks = []
        if active_tasks:
            for worker, tasks in active_tasks.items():
                for task in tasks:
                    if task["name"].startswith("performance."):
                        performance_tasks.append(
                            {
                                "worker": worker,
                                "task_id": task["id"],
                                "name": task["name"],
                                "args": task["args"],
                                "kwargs": task["kwargs"],
                                "time_start": task["time_start"],
                            }
                        )

        return performance_tasks

    @staticmethod
    def cancel_task(task_id: str):
        """Cancel a performance calculation task."""
        from src.tasks.worker import celery

        celery.control.revoke(task_id, terminate=True)
        return {"task_id": task_id, "status": "cancelled"}

    @staticmethod
    def get_scheduled_tasks():
        """Get list of scheduled performance tasks."""
        from src.tasks.worker import celery

        inspect = celery.control.inspect()
        scheduled = inspect.scheduled()

        performance_scheduled = []
        if scheduled:
            for worker, tasks in scheduled.items():
                for task in tasks:
                    if task["request"]["task"].startswith("performance."):
                        performance_scheduled.append(
                            {
                                "worker": worker,
                                "task_id": task["request"]["id"],
                                "name": task["request"]["task"],
                                "args": task["request"]["args"],
                                "kwargs": task["request"]["kwargs"],
                                "eta": task["eta"],
                            }
                        )

        return performance_scheduled


# Configuration validation
def validate_schedule_config():
    """
    Validate the performance schedule configuration.

    Returns:
        Dictionary with validation results
    """
    validation_results = {"valid": True, "errors": [], "warnings": []}

    try:
        # Check if all required tasks are defined
        required_tasks = ["daily-performance-snapshot", "refresh-performance-view"]

        for task_name in required_tasks:
            if task_name not in PERFORMANCE_SCHEDULE:
                validation_results["errors"].append(
                    f"Missing required task: {task_name}"
                )
                validation_results["valid"] = False

        # Check schedule timing
        daily_snapshot_hour = PERFORMANCE_SCHEDULE["daily-performance-snapshot"][
            "schedule"
        ].hour
        refresh_hour = PERFORMANCE_SCHEDULE["refresh-performance-view"]["schedule"].hour

        if refresh_hour <= daily_snapshot_hour:
            validation_results["warnings"].append(
                "Materialized view refresh should run after daily snapshot"
            )

        # Check timezone configuration
        for task_name, config in PERFORMANCE_SCHEDULE.items():
            if (
                hasattr(config["schedule"], "tz")
                and config["schedule"].tz != asia_kolkata
            ):
                validation_results["warnings"].append(
                    f"Task {task_name} not using IST timezone"
                )

    except Exception as e:
        validation_results["valid"] = False
        validation_results["errors"].append(f"Configuration validation error: {str(e)}")

    return validation_results


# Export for easy import
__all__ = [
    "PERFORMANCE_SCHEDULE",
    "get_performance_beat_schedule",
    "schedule_backfill_task",
    "schedule_immediate_snapshot",
    "schedule_view_refresh",
    "PerformanceTaskMonitor",
    "validate_schedule_config",
]
