"""Daily performance snapshot calculation worker."""

import asyncio
from datetime import date, datetime, timedelta
from typing import Optional

from celery import Task
from celery.utils.log import get_task_logger
from sqlalchemy.ext.asyncio import AsyncSession

from connectors.async_pg import get_async_sessionmaker
from config.logger import log_info, log_error
from utils.datetime_support import asia_kolkata
from src.tasks.worker import celery
from src.services.performance import (
    BatchPerformanceCalculator,
    TradingCalendar,
)

logger = get_task_logger(__name__)


class PerformanceSnapshotTask(Task):
    """Base task class for performance snapshot operations."""

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure."""
        log_error(f"Performance snapshot task {task_id} failed: {exc}")
        logger.error(f"Task {task_id} failed with exception: {exc}")

    def on_success(self, retval, task_id, args, kwargs):
        """Handle task success."""
        log_info(f"Performance snapshot task {task_id} completed successfully")
        logger.info(f"Task {task_id} completed successfully with result: {retval}")


@celery.task(
    bind=True,
    base=PerformanceSnapshotTask,
    name="performance.daily_snapshot",
    max_retries=3,
    default_retry_delay=300,  # 5 minutes
)
def daily_performance_snapshot(
    self,
    calculation_date: Optional[str] = None,
    asset_class_filter: Optional[str] = None,
    force_refresh: bool = False,
) -> dict:
    """
    Calculate and store daily performance snapshots for all active funds.

    Args:
        calculation_date: Date for calculations in YYYY-MM-DD format (default: today)
        asset_class_filter: Filter by asset class (optional)
        force_refresh: Force refresh even if data already exists

    Returns:
        Dictionary with task results
    """
    try:
        # Parse calculation date
        if calculation_date:
            calc_date = datetime.strptime(calculation_date, "%Y-%m-%d").date()
        else:
            calc_date = date.today()

        logger.info(f"Starting daily performance snapshot for {calc_date}")

        # Check if it's a trading day
        if not TradingCalendar.is_trading_day(calc_date) and not force_refresh:
            logger.info(f"Skipping snapshot for {calc_date} - not a trading day")
            return {
                "status": "skipped",
                "reason": "not_trading_day",
                "date": calc_date.isoformat(),
                "schemes_processed": 0,
            }

        # Run the async calculation
        result = asyncio.run(
            _run_daily_snapshot(calc_date, asset_class_filter, force_refresh)
        )

        logger.info(f"Daily snapshot completed for {calc_date}: {result}")
        return result

    except Exception as exc:
        logger.error(f"Error in daily performance snapshot: {exc}")

        # Retry logic
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying task in {self.default_retry_delay} seconds (attempt {self.request.retries + 1})"
            )
            raise self.retry(exc=exc, countdown=self.default_retry_delay)

        # Final failure
        log_error(
            f"Daily performance snapshot failed after {self.max_retries} retries: {exc}"
        )
        return {
            "status": "failed",
            "error": str(exc),
            "date": calculation_date or date.today().isoformat(),
            "schemes_processed": 0,
        }


async def _run_daily_snapshot(
    calc_date: date, asset_class_filter: Optional[str], force_refresh: bool
) -> dict:
    """
    Run the daily snapshot calculation asynchronously.

    Args:
        calc_date: Date for calculations
        asset_class_filter: Filter by asset class (optional)
        force_refresh: Force refresh even if data already exists

    Returns:
        Dictionary with results
    """
    async_sessionmaker = get_async_sessionmaker()

    async with async_sessionmaker() as session:
        try:
            # Ultra-simple: single SQL function call does everything!
            from src.services.performance.sql_calculator import SQLPerformanceCalculator

            calculator = SQLPerformanceCalculator(session)

            # Check if data already exists for this date
            if not force_refresh:
                summary = await calculator.get_calculation_summary(calc_date)
                if summary.get("total_schemes", 0) > 0:
                    logger.info(
                        f"Performance data already exists for {calc_date}, skipping"
                    )
                    return {
                        "status": "skipped",
                        "reason": "data_exists",
                        "date": calc_date.isoformat(),
                        "schemes_processed": summary["total_schemes"],
                    }

            # PostgreSQL calculates and stores all performance data
            stats = await calculator.calculate_daily_snapshots(calc_date)

            # Get final summary
            final_summary = await calculator.get_calculation_summary(calc_date)

            return {
                "status": "success",
                "date": calc_date.isoformat(),
                "schemes_processed": stats.get("schemes_processed", 0),
                "schemes_successful": stats.get("schemes_successful", 0),
                "schemes_failed": stats.get("schemes_failed", 0),
                "execution_time_seconds": stats.get("execution_time_seconds", 0),
                "summary": final_summary,
                "asset_class_filter": asset_class_filter,
            }

        except Exception as e:
            log_error(f"Error in async daily snapshot calculation: {e}")
            raise


@celery.task(
    bind=True,
    base=PerformanceSnapshotTask,
    name="performance.backfill_snapshots",
    max_retries=2,
    default_retry_delay=600,  # 10 minutes
)
def backfill_performance_snapshots(
    self, start_date: str, end_date: str, asset_class_filter: Optional[str] = None
) -> dict:
    """
    Backfill performance snapshots for a date range.

    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        asset_class_filter: Filter by asset class (optional)

    Returns:
        Dictionary with backfill results
    """
    try:
        start_dt = datetime.strptime(start_date, "%Y-%m-%d").date()
        end_dt = datetime.strptime(end_date, "%Y-%m-%d").date()

        logger.info(f"Starting backfill from {start_dt} to {end_dt}")

        # Run the async backfill
        result = asyncio.run(_run_backfill(start_dt, end_dt, asset_class_filter))

        logger.info(f"Backfill completed: {result}")
        return result

    except Exception as exc:
        logger.error(f"Error in backfill performance snapshots: {exc}")

        # Retry logic
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying backfill in {self.default_retry_delay} seconds")
            raise self.retry(exc=exc, countdown=self.default_retry_delay)

        # Final failure
        log_error(f"Backfill failed after {self.max_retries} retries: {exc}")
        return {
            "status": "failed",
            "error": str(exc),
            "start_date": start_date,
            "end_date": end_date,
            "dates_processed": 0,
        }


async def _run_backfill(
    start_date: date, end_date: date, asset_class_filter: Optional[str]
) -> dict:
    """
    Run the backfill operation asynchronously.

    Args:
        start_date: Start date for backfill
        end_date: End date for backfill
        asset_class_filter: Filter by asset class (optional)

    Returns:
        Dictionary with results
    """
    async_sessionmaker = get_async_sessionmaker()

    async with async_sessionmaker() as session:
        try:
            batch_calculator = BatchPerformanceCalculator(session)

            # Get trading days in the range
            trading_days = TradingCalendar.get_trading_days_between(
                start_date, end_date
            )

            total_schemes_processed = 0
            dates_processed = 0
            failed_dates = []

            for calc_date in trading_days:
                try:
                    logger.info(f"Processing backfill for {calc_date}")

                    schemes_count = (
                        await batch_calculator.calculate_and_store_daily_snapshots(
                            calc_date, asset_class_filter
                        )
                    )

                    total_schemes_processed += schemes_count
                    dates_processed += 1

                    logger.info(
                        f"Completed backfill for {calc_date}: {schemes_count} schemes"
                    )

                except Exception as e:
                    log_error(f"Error processing backfill for {calc_date}: {e}")
                    failed_dates.append(calc_date.isoformat())

            return {
                "status": "success",
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "total_trading_days": len(trading_days),
                "dates_processed": dates_processed,
                "total_schemes_processed": total_schemes_processed,
                "failed_dates": failed_dates,
                "asset_class_filter": asset_class_filter,
            }

        except Exception as e:
            log_error(f"Error in async backfill operation: {e}")
            raise


# Note: TimescaleDB continuous aggregates refresh automatically
# No manual refresh needed for performance_category_averages
