"""Performance domain data access objects (DAOs) using Pydantic."""

from datetime import date, datetime
from decimal import Decimal
from typing import Optional
from pydantic import BaseModel, ConfigDict, Field


class PerformanceDAO(BaseModel):
    """Performance data access object for API responses."""
    
    model_config = ConfigDict(
        title="performance",
        extra="ignore",
        from_attributes=True,
        json_encoders={
            date: lambda v: v.isoformat(),
            datetime: lambda v: v.isoformat(timespec="microseconds"),
            Decimal: lambda v: float(v) if v is not None else None,
        },
    )
    
    # Primary identification fields
    schemecode: int
    date: date
    
    # Metadata fields
    fund_name: Optional[str] = None
    aum: Optional[Decimal] = None
    asset_class: Optional[str] = None
    investment_objective: Optional[str] = None
    
    # Raw NAV field
    nav: Optional[Decimal] = None
    
    # Short-term performance metrics
    perf_1w: Optional[Decimal] = Field(None, description="1-week return (%)")
    perf_1m: Optional[Decimal] = Field(None, description="1-month return (%)")
    
    # Medium-term performance metrics
    perf_3m: Optional[Decimal] = Field(None, description="3-month return (%)")
    perf_6m: Optional[Decimal] = Field(None, description="6-month return (%)")
    perf_1y: Optional[Decimal] = Field(None, description="1-year return (%)")
    
    # Absolute returns
    absret_1y: Optional[Decimal] = Field(None, description="Absolute return over 1 year (%)")
    absret_3m: Optional[Decimal] = Field(None, description="Absolute return over 3 months (%)")
    absret_6m: Optional[Decimal] = Field(None, description="Absolute return over 6 months (%)")
    
    # Long-term CAGR metrics
    cagr_3y: Optional[Decimal] = Field(None, description="3-year CAGR (%)")
    cagr_5y: Optional[Decimal] = Field(None, description="5-year CAGR (%)")
    cagr_10y: Optional[Decimal] = Field(None, description="10-year CAGR (%)")
    
    # Rolling return metrics
    rolling_3y_avg_ret: Optional[Decimal] = Field(None, description="Average 3-year rolling return (%)")
    
    # Relative performance vs sub-category
    ret_vs_subcat_1y: Optional[Decimal] = Field(None, description="1Y return vs sub-category (%)")
    ret_vs_subcat_3y: Optional[Decimal] = Field(None, description="3Y return vs sub-category (%)")
    ret_vs_subcat_5y: Optional[Decimal] = Field(None, description="5Y return vs sub-category (%)")
    ret_vs_subcat_10y: Optional[Decimal] = Field(None, description="10Y return vs sub-category (%)")
    
    # All-time high/low metrics
    percent_from_ath: Optional[Decimal] = Field(None, description="% from all-time high (%)")
    percent_from_atl: Optional[Decimal] = Field(None, description="% from all-time low (%)")
    
    # Audit fields
    created_at: Optional[datetime] = None
    flag: Optional[str] = None


class PerformanceSummaryDAO(BaseModel):
    """Simplified performance summary for list views."""
    
    model_config = ConfigDict(
        title="performance_summary",
        extra="ignore",
        from_attributes=True,
        json_encoders={
            date: lambda v: v.isoformat(),
            Decimal: lambda v: float(v) if v is not None else None,
        },
    )
    
    schemecode: int
    fund_name: Optional[str] = None
    asset_class: Optional[str] = None
    nav: Optional[Decimal] = None
    perf_1m: Optional[Decimal] = None
    perf_1y: Optional[Decimal] = None
    cagr_3y: Optional[Decimal] = None
    cagr_5y: Optional[Decimal] = None


class PerformanceFilterDAO(BaseModel):
    """DAO for performance filtering requests."""
    
    model_config = ConfigDict(
        title="performance_filter",
        extra="ignore",
    )
    
    date: Optional[date] = Field(None, description="Snapshot date for filtering")
    asset_class: Optional[str] = Field(None, description="Filter by asset class")
    min_aum: Optional[Decimal] = Field(None, description="Minimum AUM filter")
    max_aum: Optional[Decimal] = Field(None, description="Maximum AUM filter")
    min_perf_1y: Optional[Decimal] = Field(None, description="Minimum 1Y performance")
    max_perf_1y: Optional[Decimal] = Field(None, description="Maximum 1Y performance")
    min_cagr_3y: Optional[Decimal] = Field(None, description="Minimum 3Y CAGR")
    max_cagr_3y: Optional[Decimal] = Field(None, description="Maximum 3Y CAGR")
    limit: Optional[int] = Field(100, description="Maximum number of results")
    offset: Optional[int] = Field(0, description="Pagination offset")


class TopPerformersDAO(BaseModel):
    """DAO for top performers requests."""
    
    model_config = ConfigDict(
        title="top_performers",
        extra="ignore",
    )
    
    metric: str = Field(..., description="Performance metric to rank by")
    date: date = Field(..., description="Date for ranking")
    limit: int = Field(10, description="Number of top performers to return")
    asset_class: Optional[str] = Field(None, description="Filter by asset class")
    min_aum: Optional[Decimal] = Field(None, description="Minimum AUM filter")
