"""Performance API endpoints."""

from datetime import date, datetime
from typing import List, Optional, Dict, Any
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from connectors.async_pg import get_async_session
from src.domains.performance import (
    PerformanceDAO,
    PerformanceSummaryDAO,
    PerformanceFilterDAO,
    TopPerformersDAO,
)
from src.routers.base_controller import success_response, FailureResponseDAO
from src.services.performance.performance_query_service import PerformanceQueryService
from config.logger import log_info, log_error

router = APIRouter(prefix="/performance", tags=["performance"])


@router.get(
    "/date/{target_date}",
    response_model=Dict[str, Any],
    summary="Get performance snapshot for all funds on a specific date",
    description="Returns performance data for all funds on the specified date, enabling backtesting and historical analysis.",
)
async def get_performance_by_date(
    target_date: date = Path(
        ..., description="Target date for performance snapshot (YYYY-MM-DD)"
    ),
    asset_class: Optional[str] = Query(None, description="Filter by asset class"),
    limit: int = Query(1000, ge=1, le=5000, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Pagination offset"),
    session: AsyncSession = Depends(get_async_session),
) -> Dict[str, Any]:
    """Get performance snapshot for all funds on a specific date."""
    try:
        log_info(f"Getting performance data for date: {target_date}")

        # Build query with optional filters
        where_conditions = ["date = :target_date"]
        params = {"target_date": target_date, "limit": limit, "offset": offset}

        if asset_class:
            where_conditions.append("asset_class = :asset_class")
            params["asset_class"] = asset_class

        where_clause = " AND ".join(where_conditions)

        query = text(
            f"""
            SELECT 
                schemecode, date, fund_name, aum, asset_class, investment_objective,
                nav, perf_1w, perf_1m, perf_3m, perf_6m, perf_1y,
                absret_1y, absret_3m, absret_6m,
                cagr_3y, cagr_5y, cagr_10y, rolling_3y_avg_ret,
                ret_vs_subcat_1y, ret_vs_subcat_3y, ret_vs_subcat_5y, ret_vs_subcat_10y,
                percent_from_ath, percent_from_atl, created_at, flag
            FROM performance_snapshots
            WHERE {where_clause}
            ORDER BY schemecode
            LIMIT :limit OFFSET :offset
        """
        )

        result = await session.execute(query, params)
        rows = result.fetchall()

        # Convert to PerformanceDAO objects
        performance_data = []
        for row in rows:
            performance_data.append(
                PerformanceDAO(
                    schemecode=row[0],
                    date=row[1],
                    fund_name=row[2],
                    aum=row[3],
                    asset_class=row[4],
                    investment_objective=row[5],
                    nav=row[6],
                    perf_1w=row[7],
                    perf_1m=row[8],
                    perf_3m=row[9],
                    perf_6m=row[10],
                    perf_1y=row[11],
                    absret_1y=row[12],
                    absret_3m=row[13],
                    absret_6m=row[14],
                    cagr_3y=row[15],
                    cagr_5y=row[16],
                    cagr_10y=row[17],
                    rolling_3y_avg_ret=row[18],
                    ret_vs_subcat_1y=row[19],
                    ret_vs_subcat_3y=row[20],
                    ret_vs_subcat_5y=row[21],
                    ret_vs_subcat_10y=row[22],
                    percent_from_ath=row[23],
                    percent_from_atl=row[24],
                    created_at=row[25],
                    flag=row[26],
                )
            )

        # Get total count for pagination
        count_query = text(
            f"""
            SELECT COUNT(*) FROM performance_snapshots WHERE {where_clause}
        """
        )
        count_result = await session.execute(
            count_query,
            {k: v for k, v in params.items() if k not in ["limit", "offset"]},
        )
        total_count = count_result.scalar()

        return success_response(
            {
                "date": target_date.isoformat(),
                "total_count": total_count,
                "returned_count": len(performance_data),
                "offset": offset,
                "limit": limit,
                "funds": [fund.model_dump() for fund in performance_data],
            }
        )

    except Exception as e:
        log_error(f"Error getting performance data for date {target_date}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error retrieving performance data: {str(e)}"
        )


@router.get(
    "/fund/{schemecode}/history",
    response_model=Dict[str, Any],
    summary="Get performance history for a specific fund",
    description="Returns time-series performance data for a fund, enabling trend analysis and charting.",
)
async def get_fund_performance_history(
    schemecode: int = Path(..., description="Unique fund identifier"),
    from_date: Optional[date] = Query(
        None, description="Start date for history (YYYY-MM-DD)"
    ),
    to_date: Optional[date] = Query(
        None, description="End date for history (YYYY-MM-DD)"
    ),
    limit: int = Query(365, ge=1, le=2000, description="Maximum number of records"),
    session: AsyncSession = Depends(get_async_session),
) -> Dict[str, Any]:
    """Get performance history for a specific fund."""
    try:
        log_info(f"Getting performance history for fund: {schemecode}")

        # Build date filters
        where_conditions = ["schemecode = :schemecode"]
        params = {"schemecode": schemecode, "limit": limit}

        if from_date:
            where_conditions.append("date >= :from_date")
            params["from_date"] = from_date

        if to_date:
            where_conditions.append("date <= :to_date")
            params["to_date"] = to_date

        where_clause = " AND ".join(where_conditions)

        query = text(
            f"""
            SELECT 
                schemecode, date, fund_name, aum, asset_class, investment_objective,
                nav, perf_1w, perf_1m, perf_3m, perf_6m, perf_1y,
                absret_1y, absret_3m, absret_6m,
                cagr_3y, cagr_5y, cagr_10y, rolling_3y_avg_ret,
                ret_vs_subcat_1y, ret_vs_subcat_3y, ret_vs_subcat_5y, ret_vs_subcat_10y,
                percent_from_ath, percent_from_atl, created_at, flag
            FROM performance_snapshots
            WHERE {where_clause}
            ORDER BY date DESC
            LIMIT :limit
        """
        )

        result = await session.execute(query, params)
        rows = result.fetchall()

        if not rows:
            raise HTTPException(
                status_code=404,
                detail=f"No performance data found for fund {schemecode}",
            )

        # Convert to PerformanceDAO objects
        history_data = []
        for row in rows:
            history_data.append(
                PerformanceDAO(
                    schemecode=row[0],
                    date=row[1],
                    fund_name=row[2],
                    aum=row[3],
                    asset_class=row[4],
                    investment_objective=row[5],
                    nav=row[6],
                    perf_1w=row[7],
                    perf_1m=row[8],
                    perf_3m=row[9],
                    perf_6m=row[10],
                    perf_1y=row[11],
                    absret_1y=row[12],
                    absret_3m=row[13],
                    absret_6m=row[14],
                    cagr_3y=row[15],
                    cagr_5y=row[16],
                    cagr_10y=row[17],
                    rolling_3y_avg_ret=row[18],
                    ret_vs_subcat_1y=row[19],
                    ret_vs_subcat_3y=row[20],
                    ret_vs_subcat_5y=row[21],
                    ret_vs_subcat_10y=row[22],
                    percent_from_ath=row[23],
                    percent_from_atl=row[24],
                    created_at=row[25],
                    flag=row[26],
                )
            )

        return success_response(
            {
                "schemecode": schemecode,
                "fund_name": history_data[0].fund_name if history_data else None,
                "from_date": from_date.isoformat() if from_date else None,
                "to_date": to_date.isoformat() if to_date else None,
                "record_count": len(history_data),
                "history": [record.model_dump() for record in history_data],
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error getting performance history for fund {schemecode}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error retrieving performance history: {str(e)}"
        )


@router.get(
    "/top",
    response_model=Dict[str, Any],
    summary="Get top performing funds based on a metric",
    description="Returns ranked list of top performing funds for leaderboards and rankings.",
)
async def get_top_performers(
    metric: str = Query(
        ...,
        description="Performance metric to rank by (perf_1y, cagr_3y, cagr_5y, etc.)",
    ),
    target_date: date = Query(..., description="Date for ranking"),
    limit: int = Query(
        10, ge=1, le=100, description="Number of top performers to return"
    ),
    asset_class: Optional[str] = Query(None, description="Filter by asset class"),
    min_aum: Optional[float] = Query(
        None, description="Minimum AUM filter (in crores)"
    ),
    session: AsyncSession = Depends(get_async_session),
) -> Dict[str, Any]:
    """Get top performing funds based on a specific metric."""
    try:
        log_info(f"Getting top performers for metric: {metric} on {target_date}")

        # Validate metric
        valid_metrics = [
            "perf_1w",
            "perf_1m",
            "perf_3m",
            "perf_6m",
            "perf_1y",
            "cagr_3y",
            "cagr_5y",
            "cagr_10y",
            "rolling_3y_avg_ret",
            "ret_vs_subcat_1y",
            "ret_vs_subcat_3y",
            "ret_vs_subcat_5y",
            "ret_vs_subcat_10y",
        ]

        if metric not in valid_metrics:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid metric. Must be one of: {', '.join(valid_metrics)}",
            )

        # Build query with filters
        where_conditions = [f"date = :target_date", f"{metric} IS NOT NULL"]
        params = {"target_date": target_date, "limit": limit}

        if asset_class:
            where_conditions.append("asset_class = :asset_class")
            params["asset_class"] = asset_class

        if min_aum:
            where_conditions.append("aum >= :min_aum")
            params["min_aum"] = min_aum

        where_clause = " AND ".join(where_conditions)

        query = text(
            f"""
            SELECT 
                schemecode, fund_name, asset_class, nav, aum,
                {metric} as metric_value,
                perf_1y, cagr_3y, cagr_5y
            FROM performance_snapshots
            WHERE {where_clause}
            ORDER BY {metric} DESC
            LIMIT :limit
        """
        )

        result = await session.execute(query, params)
        rows = result.fetchall()

        # Convert to summary format
        top_performers = []
        for i, row in enumerate(rows, 1):
            top_performers.append(
                {
                    "rank": i,
                    "schemecode": row[0],
                    "fund_name": row[1],
                    "asset_class": row[2],
                    "nav": float(row[3]) if row[3] else None,
                    "aum": float(row[4]) if row[4] else None,
                    "metric_value": float(row[5]) if row[5] else None,
                    "perf_1y": float(row[6]) if row[6] else None,
                    "cagr_3y": float(row[7]) if row[7] else None,
                    "cagr_5y": float(row[8]) if row[8] else None,
                }
            )

        return success_response(
            {
                "metric": metric,
                "date": target_date.isoformat(),
                "asset_class_filter": asset_class,
                "min_aum_filter": min_aum,
                "total_returned": len(top_performers),
                "top_performers": top_performers,
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error getting top performers: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error retrieving top performers: {str(e)}"
        )


@router.get(
    "/filter",
    response_model=Dict[str, Any],
    summary="Filter funds by performance criteria",
    description="Returns funds matching specified performance thresholds for dashboard filtering.",
)
async def filter_funds_by_performance(
    target_date: date = Query(..., description="Date for filtering"),
    asset_class: Optional[str] = Query(None, description="Filter by asset class"),
    min_aum: Optional[float] = Query(None, description="Minimum AUM (in crores)"),
    max_aum: Optional[float] = Query(None, description="Maximum AUM (in crores)"),
    min_perf_1y: Optional[float] = Query(
        None, description="Minimum 1Y performance (%)"
    ),
    max_perf_1y: Optional[float] = Query(
        None, description="Maximum 1Y performance (%)"
    ),
    min_cagr_3y: Optional[float] = Query(None, description="Minimum 3Y CAGR (%)"),
    max_cagr_3y: Optional[float] = Query(None, description="Maximum 3Y CAGR (%)"),
    min_cagr_5y: Optional[float] = Query(None, description="Minimum 5Y CAGR (%)"),
    max_cagr_5y: Optional[float] = Query(None, description="Maximum 5Y CAGR (%)"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Pagination offset"),
    session: AsyncSession = Depends(get_async_session),
) -> Dict[str, Any]:
    """Filter funds by performance criteria."""
    try:
        log_info(f"Filtering funds by performance criteria for date: {target_date}")

        # Build dynamic where conditions
        where_conditions = ["date = :target_date"]
        params = {"target_date": target_date, "limit": limit, "offset": offset}

        if asset_class:
            where_conditions.append("asset_class = :asset_class")
            params["asset_class"] = asset_class

        if min_aum is not None:
            where_conditions.append("aum >= :min_aum")
            params["min_aum"] = min_aum

        if max_aum is not None:
            where_conditions.append("aum <= :max_aum")
            params["max_aum"] = max_aum

        if min_perf_1y is not None:
            where_conditions.append("perf_1y >= :min_perf_1y")
            params["min_perf_1y"] = min_perf_1y

        if max_perf_1y is not None:
            where_conditions.append("perf_1y <= :max_perf_1y")
            params["max_perf_1y"] = max_perf_1y

        if min_cagr_3y is not None:
            where_conditions.append("cagr_3y >= :min_cagr_3y")
            params["min_cagr_3y"] = min_cagr_3y

        if max_cagr_3y is not None:
            where_conditions.append("cagr_3y <= :max_cagr_3y")
            params["max_cagr_3y"] = max_cagr_3y

        if min_cagr_5y is not None:
            where_conditions.append("cagr_5y >= :min_cagr_5y")
            params["min_cagr_5y"] = min_cagr_5y

        if max_cagr_5y is not None:
            where_conditions.append("cagr_5y <= :max_cagr_5y")
            params["max_cagr_5y"] = max_cagr_5y

        where_clause = " AND ".join(where_conditions)

        # Main query
        query = text(
            f"""
            SELECT
                schemecode, fund_name, asset_class, nav, aum,
                perf_1m, perf_1y, cagr_3y, cagr_5y,
                percent_from_ath, percent_from_atl
            FROM performance_snapshots
            WHERE {where_clause}
            ORDER BY perf_1y DESC NULLS LAST
            LIMIT :limit OFFSET :offset
        """
        )

        result = await session.execute(query, params)
        rows = result.fetchall()

        # Convert to summary format
        filtered_funds = []
        for row in rows:
            filtered_funds.append(
                PerformanceSummaryDAO(
                    schemecode=row[0],
                    fund_name=row[1],
                    asset_class=row[2],
                    nav=row[3],
                    perf_1m=row[5],
                    perf_1y=row[6],
                    cagr_3y=row[7],
                    cagr_5y=row[8],
                )
            )

        # Get total count
        count_query = text(
            f"""
            SELECT COUNT(*) FROM performance_snapshots WHERE {where_clause}
        """
        )
        count_result = await session.execute(
            count_query,
            {k: v for k, v in params.items() if k not in ["limit", "offset"]},
        )
        total_count = count_result.scalar()

        return success_response(
            {
                "date": target_date.isoformat(),
                "filters_applied": {
                    "asset_class": asset_class,
                    "min_aum": min_aum,
                    "max_aum": max_aum,
                    "min_perf_1y": min_perf_1y,
                    "max_perf_1y": max_perf_1y,
                    "min_cagr_3y": min_cagr_3y,
                    "max_cagr_3y": max_cagr_3y,
                    "min_cagr_5y": min_cagr_5y,
                    "max_cagr_5y": max_cagr_5y,
                },
                "total_count": total_count,
                "returned_count": len(filtered_funds),
                "offset": offset,
                "limit": limit,
                "funds": [fund.model_dump() for fund in filtered_funds],
            }
        )

    except Exception as e:
        log_error(f"Error filtering funds by performance: {e}")
        raise HTTPException(status_code=500, detail=f"Error filtering funds: {str(e)}")


@router.get(
    "/ath-analysis",
    response_model=Dict[str, Any],
    summary="Get funds' distance from all-time high",
    description="Returns funds ranked by their distance from all-time high NAV, useful for identifying re-entry opportunities.",
)
async def get_ath_analysis(
    target_date: date = Query(..., description="Date for ATH analysis"),
    asset_class: Optional[str] = Query(None, description="Filter by asset class"),
    min_decline: Optional[float] = Query(
        None, description="Minimum decline from ATH (%) - negative values"
    ),
    max_decline: Optional[float] = Query(
        None, description="Maximum decline from ATH (%) - negative values"
    ),
    min_aum: Optional[float] = Query(
        None, description="Minimum AUM filter (in crores)"
    ),
    limit: int = Query(50, ge=1, le=200, description="Maximum number of results"),
    session: AsyncSession = Depends(get_async_session),
) -> Dict[str, Any]:
    """Get funds' distance from all-time high for re-entry analysis."""
    try:
        log_info(f"Getting ATH analysis for date: {target_date}")

        # Build query conditions
        where_conditions = ["date = :target_date", "percent_from_ath IS NOT NULL"]
        params = {"target_date": target_date, "limit": limit}

        if asset_class:
            where_conditions.append("asset_class = :asset_class")
            params["asset_class"] = asset_class

        if min_aum is not None:
            where_conditions.append("aum >= :min_aum")
            params["min_aum"] = min_aum

        if min_decline is not None:
            where_conditions.append("percent_from_ath <= :min_decline")
            params["min_decline"] = min_decline

        if max_decline is not None:
            where_conditions.append("percent_from_ath >= :max_decline")
            params["max_decline"] = max_decline

        where_clause = " AND ".join(where_conditions)

        query = text(
            f"""
            SELECT
                schemecode, fund_name, asset_class, nav, aum,
                percent_from_ath, percent_from_atl,
                perf_1y, cagr_3y, cagr_5y
            FROM performance_snapshots
            WHERE {where_clause}
            ORDER BY percent_from_ath ASC
            LIMIT :limit
        """
        )

        result = await session.execute(query, params)
        rows = result.fetchall()

        # Convert to analysis format
        ath_analysis = []
        for row in rows:
            ath_analysis.append(
                {
                    "schemecode": row[0],
                    "fund_name": row[1],
                    "asset_class": row[2],
                    "current_nav": float(row[3]) if row[3] else None,
                    "aum": float(row[4]) if row[4] else None,
                    "percent_from_ath": float(row[5]) if row[5] else None,
                    "percent_from_atl": float(row[6]) if row[6] else None,
                    "perf_1y": float(row[7]) if row[7] else None,
                    "cagr_3y": float(row[8]) if row[8] else None,
                    "cagr_5y": float(row[9]) if row[9] else None,
                }
            )

        return success_response(
            {
                "date": target_date.isoformat(),
                "analysis_type": "all_time_high_distance",
                "filters_applied": {
                    "asset_class": asset_class,
                    "min_decline": min_decline,
                    "max_decline": max_decline,
                    "min_aum": min_aum,
                },
                "total_returned": len(ath_analysis),
                "funds": ath_analysis,
            }
        )

    except Exception as e:
        log_error(f"Error getting ATH analysis: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error retrieving ATH analysis: {str(e)}"
        )


# Health check endpoint for performance APIs
@router.get(
    "/health",
    response_model=Dict[str, Any],
    summary="Performance API health check",
    description="Check the health and status of performance calculation system.",
)
async def performance_health_check(
    session: AsyncSession = Depends(get_async_session),
) -> Dict[str, Any]:
    """Health check for performance APIs."""
    try:
        # Check if materialized view exists and has recent data
        query = text(
            """
            SELECT
                COUNT(*) as total_schemes,
                MAX(date) as latest_date,
                COUNT(CASE WHEN date >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as recent_data_count
            FROM performance_snapshots
        """
        )

        result = await session.execute(query)
        row = result.fetchone()

        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "performance_snapshots": {
                "total_schemes": row[0] if row else 0,
                "latest_date": row[1].isoformat() if row and row[1] else None,
                "recent_data_count": row[2] if row else 0,
            },
            "api_endpoints": [
                "/performance/date/{date}",
                "/performance/fund/{schemecode}/history",
                "/performance/top",
                "/performance/filter",
                "/performance/ath-analysis",
            ],
        }

        # Check if data is stale
        if row and row[1]:
            days_since_update = (date.today() - row[1]).days
            if days_since_update > 3:
                health_status["status"] = "warning"
                health_status["warning"] = (
                    f"Performance data is {days_since_update} days old"
                )

        return success_response(health_status)

    except Exception as e:
        log_error(f"Error in performance health check: {e}")
        return success_response(
            {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
            }
        )
