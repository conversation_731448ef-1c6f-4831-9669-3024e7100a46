#!/usr/bin/env python3
"""
Performance calculation management script.

This script provides command-line utilities for managing performance calculations.
"""

import asyncio
import argparse
import sys
from datetime import date, datetime, timedelta
from typing import Optional

# Add the project root to the path
sys.path.append('.')

from connectors.async_pg import get_async_sessionmaker
from src.services.performance import BatchPerformanceCalculator, TradingCalendar
from src.tasks.performance_scheduler import (
    schedule_immediate_snapshot,
    schedule_backfill_task,
    schedule_view_refresh,
    PerformanceTaskMonitor
)
from config.logger import log_info, log_error


async def run_immediate_calculation(
    calculation_date: Optional[date] = None,
    asset_class_filter: Optional[str] = None
):
    """Run immediate performance calculation."""
    if calculation_date is None:
        calculation_date = date.today()
    
    print(f"Running immediate performance calculation for {calculation_date}")
    
    async_sessionmaker = get_async_sessionmaker()
    
    async with async_sessionmaker() as session:
        batch_calculator = BatchPerformanceCalculator(session)
        
        # Get summary before
        before_summary = await batch_calculator.get_calculation_summary(calculation_date)
        print(f"Before: {before_summary}")
        
        # Run calculation
        schemes_processed = await batch_calculator.calculate_and_store_daily_snapshots(
            calculation_date, asset_class_filter
        )
        
        # Get summary after
        after_summary = await batch_calculator.get_calculation_summary(calculation_date)
        print(f"After: {after_summary}")
        print(f"Schemes processed: {schemes_processed}")


async def run_backfill(start_date: date, end_date: date, asset_class_filter: Optional[str] = None):
    """Run backfill operation."""
    print(f"Running backfill from {start_date} to {end_date}")
    
    trading_days = TradingCalendar.get_trading_days_between(start_date, end_date)
    print(f"Trading days to process: {len(trading_days)}")
    
    async_sessionmaker = get_async_sessionmaker()
    
    async with async_sessionmaker() as session:
        batch_calculator = BatchPerformanceCalculator(session)
        
        for calc_date in trading_days:
            print(f"Processing {calc_date}...")
            
            try:
                schemes_processed = await batch_calculator.calculate_and_store_daily_snapshots(
                    calc_date, asset_class_filter
                )
                print(f"  Processed {schemes_processed} schemes")
                
            except Exception as e:
                print(f"  Error: {e}")


async def check_data_coverage(start_date: date, end_date: date):
    """Check data coverage for a date range."""
    print(f"Checking data coverage from {start_date} to {end_date}")
    
    async_sessionmaker = get_async_sessionmaker()
    
    async with async_sessionmaker() as session:
        batch_calculator = BatchPerformanceCalculator(session)
        
        trading_days = TradingCalendar.get_trading_days_between(start_date, end_date)
        
        for calc_date in trading_days:
            summary = await batch_calculator.get_calculation_summary(calc_date)
            print(f"{calc_date}: {summary['total_schemes']} schemes, "
                  f"{summary['schemes_with_1y_data']} with 1Y data, "
                  f"{summary['schemes_with_3y_data']} with 3Y data")


def schedule_task(task_type: str, **kwargs):
    """Schedule a task using Celery."""
    print(f"Scheduling {task_type} task...")
    
    if task_type == "snapshot":
        result = schedule_immediate_snapshot(
            calculation_date=kwargs.get('calculation_date'),
            force_refresh=kwargs.get('force_refresh', True)
        )
    elif task_type == "backfill":
        result = schedule_backfill_task(
            start_date=kwargs['start_date'],
            end_date=kwargs['end_date'],
            asset_class_filter=kwargs.get('asset_class_filter')
        )
    elif task_type == "refresh":
        result = schedule_view_refresh(force=kwargs.get('force', False))
    else:
        print(f"Unknown task type: {task_type}")
        return
    
    print(f"Task scheduled with ID: {result.id}")
    return result


def monitor_tasks():
    """Monitor active performance tasks."""
    monitor = PerformanceTaskMonitor()
    
    print("Active performance tasks:")
    active_tasks = monitor.get_active_performance_tasks()
    if active_tasks:
        for task in active_tasks:
            print(f"  {task['task_id']}: {task['name']} on {task['worker']}")
    else:
        print("  No active tasks")
    
    print("\nScheduled performance tasks:")
    scheduled_tasks = monitor.get_scheduled_tasks()
    if scheduled_tasks:
        for task in scheduled_tasks:
            print(f"  {task['task_id']}: {task['name']} at {task['eta']}")
    else:
        print("  No scheduled tasks")


def get_task_status(task_id: str):
    """Get status of a specific task."""
    monitor = PerformanceTaskMonitor()
    status = monitor.get_task_status(task_id)
    
    print(f"Task {task_id}:")
    print(f"  Status: {status['status']}")
    print(f"  Result: {status['result']}")
    if status['traceback']:
        print(f"  Error: {status['traceback']}")
    if status['date_done']:
        print(f"  Completed: {status['date_done']}")


def main():
    """Main command-line interface."""
    parser = argparse.ArgumentParser(description="Performance calculation management")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Immediate calculation
    calc_parser = subparsers.add_parser('calculate', help='Run immediate calculation')
    calc_parser.add_argument('--date', type=str, help='Calculation date (YYYY-MM-DD)')
    calc_parser.add_argument('--asset-class', type=str, help='Filter by asset class')
    
    # Backfill
    backfill_parser = subparsers.add_parser('backfill', help='Run backfill operation')
    backfill_parser.add_argument('start_date', type=str, help='Start date (YYYY-MM-DD)')
    backfill_parser.add_argument('end_date', type=str, help='End date (YYYY-MM-DD)')
    backfill_parser.add_argument('--asset-class', type=str, help='Filter by asset class')
    
    # Check coverage
    coverage_parser = subparsers.add_parser('coverage', help='Check data coverage')
    coverage_parser.add_argument('start_date', type=str, help='Start date (YYYY-MM-DD)')
    coverage_parser.add_argument('end_date', type=str, help='End date (YYYY-MM-DD)')
    
    # Schedule tasks
    schedule_parser = subparsers.add_parser('schedule', help='Schedule a task')
    schedule_parser.add_argument('task_type', choices=['snapshot', 'backfill', 'refresh'])
    schedule_parser.add_argument('--date', type=str, help='Calculation date (YYYY-MM-DD)')
    schedule_parser.add_argument('--start-date', type=str, help='Start date for backfill')
    schedule_parser.add_argument('--end-date', type=str, help='End date for backfill')
    schedule_parser.add_argument('--asset-class', type=str, help='Filter by asset class')
    schedule_parser.add_argument('--force', action='store_true', help='Force refresh')
    
    # Monitor tasks
    subparsers.add_parser('monitor', help='Monitor active tasks')
    
    # Task status
    status_parser = subparsers.add_parser('status', help='Get task status')
    status_parser.add_argument('task_id', type=str, help='Task ID')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'calculate':
            calc_date = None
            if args.date:
                calc_date = datetime.strptime(args.date, '%Y-%m-%d').date()
            
            asyncio.run(run_immediate_calculation(calc_date, args.asset_class))
        
        elif args.command == 'backfill':
            start_date = datetime.strptime(args.start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(args.end_date, '%Y-%m-%d').date()
            
            asyncio.run(run_backfill(start_date, end_date, args.asset_class))
        
        elif args.command == 'coverage':
            start_date = datetime.strptime(args.start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(args.end_date, '%Y-%m-%d').date()
            
            asyncio.run(check_data_coverage(start_date, end_date))
        
        elif args.command == 'schedule':
            kwargs = {}
            if args.date:
                kwargs['calculation_date'] = args.date
            if args.start_date:
                kwargs['start_date'] = args.start_date
            if args.end_date:
                kwargs['end_date'] = args.end_date
            if args.asset_class:
                kwargs['asset_class_filter'] = args.asset_class
            if args.force:
                kwargs['force'] = True
                kwargs['force_refresh'] = True
            
            schedule_task(args.task_type, **kwargs)
        
        elif args.command == 'monitor':
            monitor_tasks()
        
        elif args.command == 'status':
            get_task_status(args.task_id)
    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
