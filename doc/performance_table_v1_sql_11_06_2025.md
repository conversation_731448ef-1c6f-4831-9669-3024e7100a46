# Performance Snapshot Feature Implementation Guide
**Version 1.0 | Date: November 6, 2025**

## Overview

The Performance Snapshot feature is a comprehensive system that calculates and stores daily performance metrics for mutual funds. It leverages TimescaleDB for optimal time-series data handling and implements a SQL-first approach for maximum performance.

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Daily Worker  │───▶│  SQL Calculator  │───▶│ Performance DB  │
│   (Scheduler)   │    │   (PostgreSQL)   │    │  (TimescaleDB)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                       │
         ▼                        ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Trading Calendar│    │   NAV Retrieval  │    │   API Endpoints │
│   (Holidays)    │    │ (Holiday-Safe)   │    │   (5 Routes)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Core Components

### 1. Performance Snapshots Table (TimescaleDB Hypertable)

**Location**: `db/versions/performance_001_create_hypertable.py`

The table stores 25 columns of performance data:

#### Metadata Fields (6 columns)
- `schemecode` - Unique fund identifier
- `date` - Snapshot date (daily granularity)
- `fund_name` - Name of the mutual fund scheme
- `aum` - Assets Under Management
- `asset_class` - Equity, Debt, Hybrid, etc.
- `investment_objective` - Growth, Income, Balanced

#### NAV Field (1 column)
- `nav` - Net Asset Value on snapshot date

#### Performance Metrics (18 columns)
- **Short-term**: `perf_1w`, `perf_1m`
- **Medium-term**: `perf_3m`, `perf_6m`, `perf_1y`
- **Absolute returns**: `absret_1y`, `absret_3m`, `absret_6m`
- **CAGR metrics**: `cagr_3y`, `cagr_5y`, `cagr_10y`
- **Rolling averages**: `rolling_3y_avg_ret`
- **Relative performance**: `ret_vs_subcat_1y`, `ret_vs_subcat_3y`, `ret_vs_subcat_5y`, `ret_vs_subcat_10y`
- **ATH/ATL analysis**: `percent_from_ath`, `percent_from_atl`

```sql
-- TimescaleDB Configuration
SELECT create_hypertable(
    'performance_snapshots', 
    'date',
    chunk_time_interval => INTERVAL '1 month',
    partitioning_column => 'schemecode',
    number_partitions => 50
);
```

### 2. Holiday-Safe NAV Retrieval System

**Location**: `src/services/performance/nav_utils.py` & `db/versions/performance_002_nav_functions.py`

#### Key Function: `get_nav_safe()`

```sql
CREATE OR REPLACE FUNCTION get_nav_safe(
    p_schemecode BIGINT,
    p_target_date DATE
) RETURNS NUMERIC(18,6) AS $$
DECLARE
    nav_value NUMERIC(18,6);
BEGIN
    -- Get the most recent NAV on or before the target date
    -- This handles weekends and market holidays automatically
    SELECT navrs INTO nav_value
    FROM navhist
    WHERE schemecode = p_schemecode
      AND navdate <= p_target_date
      AND navrs > 0  -- Exclude invalid NAV values
    ORDER BY navdate DESC
    LIMIT 1;
    
    RETURN nav_value;
END;
$$ LANGUAGE plpgsql STABLE;
```

**How it works:**
1. **Backward Lookback**: Always looks backward from target date
2. **Holiday Handling**: Automatically finds the last available trading day
3. **Data Validation**: Excludes invalid NAV values (navrs > 0)
4. **Fallback Logic**: Returns NULL if no valid NAV found within reasonable timeframe

#### Batch NAV Retrieval

```sql
-- Gets all NAV points needed for performance calculations in one call
SELECT * FROM get_nav_points(schemecode, base_date);
-- Returns: nav_current, nav_1w, nav_1m, nav_3m, nav_6m, nav_1y, nav_3y, nav_5y, nav_10y
```

### 3. Trading Calendar System

**Location**: `src/services/performance/trading_calendar.py`

Handles Indian market holidays and weekends:

```python
@classmethod
def is_trading_day(cls, check_date: date) -> bool:
    """Check if a date is a trading day (not weekend or holiday)."""
    return not (
        cls.is_weekend(check_date) or
        cls.is_fixed_holiday(check_date) or
        cls.is_variable_holiday(check_date)
    )
```

**Features:**
- Fixed holidays (Independence Day, Republic Day, etc.)
- Variable holidays (Diwali, Eid, etc.)
- Weekend detection
- Market hours validation (9:15 AM to 3:30 PM IST)

### 4. SQL-First Performance Calculation Engine

**Location**: `db/versions/performance_003_calculation_functions.py` & `performance_004_master_function.py`

#### Master Calculation Function

```sql
CREATE OR REPLACE FUNCTION calculate_performance_metrics(
    p_schemecode BIGINT,
    p_date DATE
) RETURNS TABLE(
    schemecode BIGINT,
    date DATE,
    fund_name TEXT,
    aum NUMERIC(18,6),
    asset_class TEXT,
    investment_objective TEXT,
    nav NUMERIC(18,6),
    perf_1w NUMERIC(18,6),
    perf_1m NUMERIC(18,6),
    perf_3m NUMERIC(18,6),
    perf_6m NUMERIC(18,6),
    perf_1y NUMERIC(18,6),
    absret_1y NUMERIC(18,6),
    absret_3m NUMERIC(18,6),
    absret_6m NUMERIC(18,6),
    cagr_3y NUMERIC(18,6),
    cagr_5y NUMERIC(18,6),
    cagr_10y NUMERIC(18,6),
    rolling_3y_avg_ret NUMERIC(18,6),
    percent_from_ath NUMERIC(18,6),
    percent_from_atl NUMERIC(18,6)
) AS $$
```

**Calculation Steps:**
1. **NAV Retrieval**: Get all required NAV points using `get_nav_points()`
2. **ATH/ATL Analysis**: Get all-time high/low using `get_ath_atl()`
3. **Metadata Lookup**: Get fund details using `get_scheme_metadata()`
4. **Performance Calculation**: Calculate all metrics using `calculate_all_performance_metrics()`
5. **Return Results**: Return complete performance record

#### Daily Refresh Function

```sql
CREATE OR REPLACE FUNCTION refresh_performance_data(
    p_date DATE DEFAULT CURRENT_DATE
) RETURNS TABLE(
    schemes_processed INTEGER,
    schemes_successful INTEGER,
    schemes_failed INTEGER,
    execution_time_seconds NUMERIC
) AS $$
```

**Process:**
1. Get all active schemes from master data
2. Calculate performance for each scheme
3. Insert/update performance_snapshots table
4. Return processing statistics
5. Handle errors gracefully with detailed logging

### 5. Daily Snapshot Worker

**Location**: `src/tasks/performance_snapshot_worker.py`

#### Main Worker Function

```python
async def run_daily_performance_snapshot(
    calculation_date: str = None,
    asset_class_filter: str = None,
    force_refresh: bool = False
) -> dict:
```

**Workflow:**
1. **Date Validation**: Parse and validate calculation date
2. **Trading Day Check**: Skip if not a trading day (unless forced)
3. **Duplicate Check**: Skip if data already exists (unless forced)
4. **SQL Execution**: Call `refresh_performance_data()` function
5. **Result Logging**: Log processing statistics

#### Backfill Support

```python
async def run_performance_backfill(
    start_date: str,
    end_date: str,
    asset_class_filter: str = None
) -> dict:
```

**Features:**
- Processes multiple dates in sequence
- Only processes trading days
- Handles failures gracefully
- Provides detailed progress reporting

## Step-by-Step Implementation Flow

### Daily Snapshot Process

1. **Scheduler Trigger**
   - Celery worker triggers daily at market close
   - Checks if current date is a trading day

2. **Data Validation**
   - Verify NAV data availability
   - Check for existing performance data

3. **SQL Calculation Pipeline**
   ```
   get_nav_safe() → get_nav_points() → calculate_performance_metrics() → refresh_performance_data()
   ```

4. **Database Storage**
   - Insert into TimescaleDB hypertable
   - Automatic partitioning by date and schemecode

5. **Result Verification**
   - Count successful/failed calculations
   - Log processing statistics

### Holiday-Safe NAV Retrieval Flow

1. **Target Date Input**: Receive calculation date
2. **Backward Search**: Look for NAV on or before target date
3. **Holiday Handling**: Automatically skip weekends/holidays
4. **Data Validation**: Ensure NAV > 0
5. **Fallback Logic**: Return NULL if no valid data found

### Performance Calculation Flow

1. **NAV Collection**: Get all required NAV points (current, 1w, 1m, 3m, 6m, 1y, 3y, 5y, 10y)
2. **Simple Returns**: Calculate percentage returns for short/medium term
3. **CAGR Calculation**: Annualized returns for long-term periods
4. **ATH/ATL Analysis**: Distance from all-time high/low
5. **Relative Performance**: Compare against subcategory benchmarks

## API Endpoints (5 Routes)

**Location**: `src/routers/performance_controller.py`

### 1. Get Performance by Date
```
GET /performance/date/{date}
```
- Returns all funds' performance for a specific date
- Supports asset class filtering
- Pagination support

### 2. Fund Performance History
```
GET /performance/fund/{schemecode}/history
```
- Time-series data for a specific fund
- Date range filtering
- Useful for charting and trend analysis

### 3. Top Performers
```
GET /performance/top
```
- Ranked list by any performance metric
- Asset class and AUM filtering
- Configurable ranking criteria

### 4. Performance Filtering
```
GET /performance/filter
```
- Advanced filtering by multiple criteria
- AUM, performance, asset class filters
- Pagination and sorting

### 5. ATH Analysis
```
GET /performance/ath-analysis
```
- Funds ranked by distance from all-time high
- Useful for identifying re-entry opportunities
- Decline percentage filtering

## Key Benefits

### 1. **SQL-First Approach**
- Maximum performance through database optimization
- Reduced Python processing overhead
- Leverages PostgreSQL's analytical capabilities

### 2. **Holiday-Safe Logic**
- Automatic handling of market holidays
- Backward lookback ensures data availability
- No manual holiday calendar maintenance

### 3. **TimescaleDB Optimization**
- Efficient time-series data storage
- Automatic partitioning and compression
- Optimized for analytical queries

### 4. **Immutable Historical Records**
- Daily snapshots preserve historical performance
- No recalculation of past data
- Audit trail for performance analysis

### 5. **Comprehensive Metrics**
- 18 different performance calculations
- Short, medium, and long-term analysis
- Relative and absolute performance measures

## Error Handling & Monitoring

### 1. **Data Validation**
- NAV value validation (> 0)
- Date range validation
- Scheme existence checks

### 2. **Graceful Degradation**
- NULL handling for missing data
- Partial calculations when possible
- Detailed error logging

### 3. **Health Monitoring**
```
GET /performance/health
```
- Data freshness checks
- Processing statistics
- System status monitoring

## Performance Considerations

### 1. **Database Optimization**
- TimescaleDB hypertable partitioning
- Composite primary key (schemecode, date)
- Optimized indexes for common queries

### 2. **Batch Processing**
- Single SQL function processes all schemes
- Minimal Python overhead
- Efficient memory usage

### 3. **Caching Strategy**
- Immutable daily snapshots
- No need for cache invalidation
- Direct database queries for real-time data

## Future Enhancements

1. **Compression**: Add TimescaleDB compression for older data
2. **Continuous Aggregates**: Pre-computed monthly/yearly summaries
3. **Real-time Updates**: Intraday performance calculations
4. **Benchmarking**: Enhanced relative performance metrics
5. **Risk Metrics**: Volatility, Sharpe ratio, maximum drawdown

---

**Note**: The `get_nav_safe` function is indeed implemented and is the core component that enables holiday-safe NAV retrieval throughout the system. It's defined in the database migration `performance_002_nav_functions.py` and used extensively by all performance calculation functions.
