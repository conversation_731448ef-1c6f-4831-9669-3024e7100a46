"""create_master_performance_function

Revision ID: performance_004
Revises: performance_003
Create Date: 2025-07-11 13:30:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "performance_004"
down_revision: Union[str, None] = "performance_003"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create master performance calculation function."""
    
    # 1. Master function that calculates complete performance metrics for a single scheme
    op.execute(
        """
        CREATE OR REPLACE FUNCTION calculate_performance_metrics(
            p_schemecode BIGINT,
            p_date DATE
        ) RETURNS TABLE(
            schemecode BIGINT,
            date DATE,
            fund_name TEXT,
            aum NUMERIC(18,6),
            asset_class TEXT,
            investment_objective TEXT,
            nav NUMERIC(18,6),
            perf_1w NUMERIC(18,6),
            perf_1m NUMERIC(18,6),
            perf_3m NUMERIC(18,6),
            perf_6m NUMERIC(18,6),
            perf_1y NUMERIC(18,6),
            absret_1y NUMERIC(18,6),
            absret_3m NUMERIC(18,6),
            absret_6m NUMERIC(18,6),
            cagr_3y NUMERIC(18,6),
            cagr_5y NUMERIC(18,6),
            cagr_10y NUMERIC(18,6),
            rolling_3y_avg_ret NUMERIC(18,6),
            percent_from_ath NUMERIC(18,6),
            percent_from_atl NUMERIC(18,6)
        ) AS $$
        DECLARE
            nav_points RECORD;
            ath_atl RECORD;
            metadata RECORD;
            performance_metrics RECORD;
        BEGIN
            -- Get all NAV points needed for calculations
            SELECT * INTO nav_points FROM get_nav_points(p_schemecode, p_date);
            
            -- Get ATH/ATL data
            SELECT * INTO ath_atl FROM get_ath_atl(p_schemecode, p_date);
            
            -- Get scheme metadata
            SELECT * INTO metadata FROM get_scheme_metadata(p_schemecode);
            
            -- Calculate all performance metrics
            SELECT * INTO performance_metrics 
            FROM calculate_all_performance_metrics(
                nav_points.nav_current,
                nav_points.nav_1w,
                nav_points.nav_1m,
                nav_points.nav_3m,
                nav_points.nav_6m,
                nav_points.nav_1y,
                nav_points.nav_3y,
                nav_points.nav_5y,
                nav_points.nav_10y,
                ath_atl.ath_nav,
                ath_atl.atl_nav
            );
            
            -- Return complete performance record
            RETURN QUERY
            SELECT 
                p_schemecode,
                p_date,
                metadata.fund_name,
                metadata.aum,
                metadata.asset_class,
                metadata.investment_objective,
                nav_points.nav_current,
                performance_metrics.perf_1w,
                performance_metrics.perf_1m,
                performance_metrics.perf_3m,
                performance_metrics.perf_6m,
                performance_metrics.perf_1y,
                performance_metrics.absret_1y,
                performance_metrics.absret_3m,
                performance_metrics.absret_6m,
                performance_metrics.cagr_3y,
                performance_metrics.cagr_5y,
                performance_metrics.cagr_10y,
                calculate_rolling_3y_return(p_schemecode, p_date),
                performance_metrics.percent_from_ath,
                performance_metrics.percent_from_atl;
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )
    
    # 2. Batch calculation function for multiple schemes
    op.execute(
        """
        CREATE OR REPLACE FUNCTION calculate_batch_performance(
            p_schemecodes BIGINT[],
            p_date DATE DEFAULT CURRENT_DATE
        ) RETURNS TABLE(
            schemecode BIGINT,
            date DATE,
            fund_name TEXT,
            aum NUMERIC(18,6),
            asset_class TEXT,
            investment_objective TEXT,
            nav NUMERIC(18,6),
            perf_1w NUMERIC(18,6),
            perf_1m NUMERIC(18,6),
            perf_3m NUMERIC(18,6),
            perf_6m NUMERIC(18,6),
            perf_1y NUMERIC(18,6),
            absret_1y NUMERIC(18,6),
            absret_3m NUMERIC(18,6),
            absret_6m NUMERIC(18,6),
            cagr_3y NUMERIC(18,6),
            cagr_5y NUMERIC(18,6),
            cagr_10y NUMERIC(18,6),
            rolling_3y_avg_ret NUMERIC(18,6),
            percent_from_ath NUMERIC(18,6),
            percent_from_atl NUMERIC(18,6)
        ) AS $$
        DECLARE
            scheme_id BIGINT;
        BEGIN
            -- Calculate performance for each scheme in the array
            FOREACH scheme_id IN ARRAY p_schemecodes
            LOOP
                RETURN QUERY
                SELECT * FROM calculate_performance_metrics(scheme_id, p_date);
            END LOOP;
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )
    
    # 3. Complete daily refresh function (calculates and stores all active schemes)
    op.execute(
        """
        CREATE OR REPLACE FUNCTION refresh_performance_data(
            p_date DATE DEFAULT CURRENT_DATE
        ) RETURNS TABLE(
            schemes_processed INTEGER,
            schemes_successful INTEGER,
            schemes_failed INTEGER,
            execution_time_seconds NUMERIC
        ) AS $$
        DECLARE
            start_time TIMESTAMP;
            end_time TIMESTAMP;
            total_schemes INTEGER := 0;
            successful_schemes INTEGER := 0;
            failed_schemes INTEGER := 0;
            scheme_record RECORD;
        BEGIN
            start_time := clock_timestamp();
            
            -- Process all active schemes with recent NAV data
            FOR scheme_record IN 
                SELECT DISTINCT sm.schemecode
                FROM scheme_master sm
                WHERE sm.flag != 'D'  -- Active schemes only
                  AND EXISTS (
                      SELECT 1 FROM navhist n 
                      WHERE n.schemecode = sm.schemecode 
                        AND n.navdate >= p_date - INTERVAL '30 days'
                        AND n.navrs > 0
                  )
                ORDER BY sm.schemecode
            LOOP
                total_schemes := total_schemes + 1;
                
                BEGIN
                    -- Insert or update performance snapshot
                    INSERT INTO performance_snapshots (
                        schemecode, date, fund_name, aum, asset_class, investment_objective,
                        nav, perf_1w, perf_1m, perf_3m, perf_6m, perf_1y,
                        absret_1y, absret_3m, absret_6m,
                        cagr_3y, cagr_5y, cagr_10y, rolling_3y_avg_ret,
                        percent_from_ath, percent_from_atl,
                        created_at, updated_at, flag
                    )
                    SELECT 
                        schemecode, date, fund_name, aum, asset_class, investment_objective,
                        nav, perf_1w, perf_1m, perf_3m, perf_6m, perf_1y,
                        absret_1y, absret_3m, absret_6m,
                        cagr_3y, cagr_5y, cagr_10y, rolling_3y_avg_ret,
                        percent_from_ath, percent_from_atl,
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'A'
                    FROM calculate_performance_metrics(scheme_record.schemecode, p_date)
                    ON CONFLICT (schemecode, date) 
                    DO UPDATE SET
                        fund_name = EXCLUDED.fund_name,
                        aum = EXCLUDED.aum,
                        asset_class = EXCLUDED.asset_class,
                        investment_objective = EXCLUDED.investment_objective,
                        nav = EXCLUDED.nav,
                        perf_1w = EXCLUDED.perf_1w,
                        perf_1m = EXCLUDED.perf_1m,
                        perf_3m = EXCLUDED.perf_3m,
                        perf_6m = EXCLUDED.perf_6m,
                        perf_1y = EXCLUDED.perf_1y,
                        absret_1y = EXCLUDED.absret_1y,
                        absret_3m = EXCLUDED.absret_3m,
                        absret_6m = EXCLUDED.absret_6m,
                        cagr_3y = EXCLUDED.cagr_3y,
                        cagr_5y = EXCLUDED.cagr_5y,
                        cagr_10y = EXCLUDED.cagr_10y,
                        rolling_3y_avg_ret = EXCLUDED.rolling_3y_avg_ret,
                        percent_from_ath = EXCLUDED.percent_from_ath,
                        percent_from_atl = EXCLUDED.percent_from_atl,
                        updated_at = CURRENT_TIMESTAMP;
                    
                    successful_schemes := successful_schemes + 1;
                    
                EXCEPTION WHEN OTHERS THEN
                    -- Log error but continue processing
                    failed_schemes := failed_schemes + 1;
                    RAISE NOTICE 'Failed to process scheme %: %', scheme_record.schemecode, SQLERRM;
                END;
            END LOOP;
            
            end_time := clock_timestamp();
            
            RETURN QUERY
            SELECT 
                total_schemes,
                successful_schemes,
                failed_schemes,
                EXTRACT(EPOCH FROM (end_time - start_time))::NUMERIC;
        END;
        $$ LANGUAGE plpgsql;
        """
    )
    
    # 4. Performance summary function
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_performance_summary(
            p_date DATE DEFAULT CURRENT_DATE
        ) RETURNS TABLE(
            total_schemes INTEGER,
            schemes_with_1y_data INTEGER,
            schemes_with_3y_data INTEGER,
            schemes_with_5y_data INTEGER,
            schemes_with_10y_data INTEGER,
            avg_perf_1y NUMERIC(18,6),
            avg_cagr_3y NUMERIC(18,6),
            avg_cagr_5y NUMERIC(18,6),
            top_performer_1y BIGINT,
            top_performer_3y BIGINT
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                COUNT(*)::INTEGER as total_schemes,
                COUNT(CASE WHEN perf_1y IS NOT NULL THEN 1 END)::INTEGER as schemes_with_1y_data,
                COUNT(CASE WHEN cagr_3y IS NOT NULL THEN 1 END)::INTEGER as schemes_with_3y_data,
                COUNT(CASE WHEN cagr_5y IS NOT NULL THEN 1 END)::INTEGER as schemes_with_5y_data,
                COUNT(CASE WHEN cagr_10y IS NOT NULL THEN 1 END)::INTEGER as schemes_with_10y_data,
                ROUND(AVG(perf_1y), 2) as avg_perf_1y,
                ROUND(AVG(cagr_3y), 2) as avg_cagr_3y,
                ROUND(AVG(cagr_5y), 2) as avg_cagr_5y,
                (SELECT schemecode FROM performance_snapshots 
                 WHERE date = p_date AND perf_1y IS NOT NULL 
                 ORDER BY perf_1y DESC LIMIT 1) as top_performer_1y,
                (SELECT schemecode FROM performance_snapshots 
                 WHERE date = p_date AND cagr_3y IS NOT NULL 
                 ORDER BY cagr_3y DESC LIMIT 1) as top_performer_3y
            FROM performance_snapshots
            WHERE date = p_date;
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )


def downgrade() -> None:
    """Drop master performance functions."""
    op.execute("DROP FUNCTION IF EXISTS get_performance_summary(DATE);")
    op.execute("DROP FUNCTION IF EXISTS refresh_performance_data(DATE);")
    op.execute("DROP FUNCTION IF EXISTS calculate_batch_performance(BIGINT[], DATE);")
    op.execute("DROP FUNCTION IF EXISTS calculate_performance_metrics(BIGINT, DATE);")
