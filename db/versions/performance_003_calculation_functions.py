"""create_calculation_functions

Revision ID: performance_003
Revises: performance_002
Create Date: 2025-07-11 13:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "performance_003"
down_revision: Union[str, None] = "performance_002"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create performance calculation functions."""
    
    # 1. Simple return calculation (for short-term metrics)
    op.execute(
        """
        CREATE OR REPLACE FUNCTION calculate_return(
            current_nav NUMERIC(18,6), 
            previous_nav NUMERIC(18,6)
        ) RETURNS NUMERIC(18,6) AS $$
        BEGIN
            -- Handle null or zero values
            IF previous_nav IS NULL OR previous_nav = 0 THEN
                RETURN NULL;
            END IF;
            
            -- Calculate simple return: ((current - previous) / previous) * 100
            RETURN ROUND(((current_nav - previous_nav) / previous_nav * 100)::NUMERIC, 6);
        END;
        $$ LANGUAGE plpgsql IMMUTABLE;
        """
    )
    
    # 2. CAGR calculation (for long-term metrics)
    op.execute(
        """
        CREATE OR REPLACE FUNCTION calculate_cagr(
            current_nav NUMERIC(18,6), 
            initial_nav NUMERIC(18,6), 
            years NUMERIC
        ) RETURNS NUMERIC(18,6) AS $$
        BEGIN
            -- Handle null, zero, or invalid values
            IF initial_nav IS NULL OR initial_nav = 0 OR years = 0 OR current_nav IS NULL THEN
                RETURN NULL;
            END IF;
            
            -- Calculate CAGR: ((current/initial)^(1/years) - 1) * 100
            RETURN ROUND(((POWER(current_nav / initial_nav, 1.0 / years) - 1) * 100)::NUMERIC, 6);
        END;
        $$ LANGUAGE plpgsql IMMUTABLE;
        """
    )
    
    # 3. ATH/ATL percentage calculation
    op.execute(
        """
        CREATE OR REPLACE FUNCTION calculate_ath_atl_percentages(
            current_nav NUMERIC(18,6),
            ath_nav NUMERIC(18,6),
            atl_nav NUMERIC(18,6)
        ) RETURNS TABLE(
            percent_from_ath NUMERIC(18,6),
            percent_from_atl NUMERIC(18,6)
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                CASE 
                    WHEN ath_nav IS NOT NULL AND ath_nav > 0 
                    THEN ROUND(((current_nav / ath_nav - 1) * 100)::NUMERIC, 6)
                    ELSE NULL 
                END as percent_from_ath,
                CASE 
                    WHEN atl_nav IS NOT NULL AND atl_nav > 0 
                    THEN ROUND(((current_nav / atl_nav - 1) * 100)::NUMERIC, 6)
                    ELSE NULL 
                END as percent_from_atl;
        END;
        $$ LANGUAGE plpgsql IMMUTABLE;
        """
    )
    
    # 4. Rolling return calculation (simplified 3Y average)
    op.execute(
        """
        CREATE OR REPLACE FUNCTION calculate_rolling_3y_return(
            p_schemecode BIGINT,
            p_end_date DATE
        ) RETURNS NUMERIC(18,6) AS $$
        DECLARE
            current_nav NUMERIC(18,6);
            start_nav NUMERIC(18,6);
        BEGIN
            -- Get current NAV
            current_nav := get_nav_safe(p_schemecode, p_end_date);
            
            -- Get NAV from 3 years ago
            start_nav := get_nav_safe(p_schemecode, p_end_date - INTERVAL '3 years');
            
            -- Calculate 3Y CAGR as proxy for rolling return
            RETURN calculate_cagr(current_nav, start_nav, 3);
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )
    
    # 5. Relative performance calculation
    op.execute(
        """
        CREATE OR REPLACE FUNCTION calculate_relative_performance(
            fund_return NUMERIC(18,6),
            category_avg_return NUMERIC(18,6)
        ) RETURNS NUMERIC(18,6) AS $$
        BEGIN
            -- Calculate outperformance vs category
            IF fund_return IS NULL OR category_avg_return IS NULL THEN
                RETURN NULL;
            END IF;
            
            RETURN ROUND((fund_return - category_avg_return)::NUMERIC, 6);
        END;
        $$ LANGUAGE plpgsql IMMUTABLE;
        """
    )
    
    # 6. Comprehensive performance metrics calculation
    op.execute(
        """
        CREATE OR REPLACE FUNCTION calculate_all_performance_metrics(
            nav_current NUMERIC(18,6),
            nav_1w NUMERIC(18,6),
            nav_1m NUMERIC(18,6),
            nav_3m NUMERIC(18,6),
            nav_6m NUMERIC(18,6),
            nav_1y NUMERIC(18,6),
            nav_3y NUMERIC(18,6),
            nav_5y NUMERIC(18,6),
            nav_10y NUMERIC(18,6),
            ath_nav NUMERIC(18,6),
            atl_nav NUMERIC(18,6)
        ) RETURNS TABLE(
            perf_1w NUMERIC(18,6),
            perf_1m NUMERIC(18,6),
            perf_3m NUMERIC(18,6),
            perf_6m NUMERIC(18,6),
            perf_1y NUMERIC(18,6),
            absret_1y NUMERIC(18,6),
            absret_3m NUMERIC(18,6),
            absret_6m NUMERIC(18,6),
            cagr_3y NUMERIC(18,6),
            cagr_5y NUMERIC(18,6),
            cagr_10y NUMERIC(18,6),
            percent_from_ath NUMERIC(18,6),
            percent_from_atl NUMERIC(18,6)
        ) AS $$
        DECLARE
            ath_atl_result RECORD;
        BEGIN
            -- Calculate ATH/ATL percentages
            SELECT * INTO ath_atl_result 
            FROM calculate_ath_atl_percentages(nav_current, ath_nav, atl_nav);
            
            RETURN QUERY
            SELECT 
                -- Simple returns
                calculate_return(nav_current, nav_1w) as perf_1w,
                calculate_return(nav_current, nav_1m) as perf_1m,
                calculate_return(nav_current, nav_3m) as perf_3m,
                calculate_return(nav_current, nav_6m) as perf_6m,
                calculate_return(nav_current, nav_1y) as perf_1y,
                
                -- Absolute returns (same as simple returns)
                calculate_return(nav_current, nav_1y) as absret_1y,
                calculate_return(nav_current, nav_3m) as absret_3m,
                calculate_return(nav_current, nav_6m) as absret_6m,
                
                -- CAGR calculations
                calculate_cagr(nav_current, nav_3y, 3) as cagr_3y,
                calculate_cagr(nav_current, nav_5y, 5) as cagr_5y,
                calculate_cagr(nav_current, nav_10y, 10) as cagr_10y,
                
                -- ATH/ATL percentages
                ath_atl_result.percent_from_ath,
                ath_atl_result.percent_from_atl;
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )
    
    # 7. Data validation function
    op.execute(
        """
        CREATE OR REPLACE FUNCTION validate_performance_data(
            p_schemecode BIGINT,
            p_date DATE
        ) RETURNS TABLE(
            validation_check TEXT,
            is_valid BOOLEAN,
            message TEXT
        ) AS $$
        DECLARE
            current_nav NUMERIC(18,6);
            nav_count INTEGER;
        BEGIN
            -- Get current NAV
            current_nav := get_nav_safe(p_schemecode, p_date);
            
            -- Check 1: Current NAV exists
            RETURN QUERY
            SELECT 
                'current_nav_exists'::TEXT,
                (current_nav IS NOT NULL AND current_nav > 0),
                CASE 
                    WHEN current_nav IS NULL THEN 'No NAV found for date'
                    WHEN current_nav <= 0 THEN 'Invalid NAV value'
                    ELSE 'Current NAV is valid'
                END;
            
            -- Check 2: Sufficient historical data
            SELECT COUNT(*) INTO nav_count
            FROM navhist
            WHERE schemecode = p_schemecode
              AND navdate <= p_date
              AND navdate >= p_date - INTERVAL '1 year'
              AND navrs > 0;
            
            RETURN QUERY
            SELECT 
                'sufficient_history'::TEXT,
                (nav_count >= 200),  -- At least 200 trading days in past year
                CASE 
                    WHEN nav_count < 50 THEN 'Insufficient data - less than 50 days'
                    WHEN nav_count < 200 THEN 'Limited data - less than 200 days'
                    ELSE 'Sufficient historical data'
                END;
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )


def downgrade() -> None:
    """Drop calculation functions."""
    op.execute("DROP FUNCTION IF EXISTS validate_performance_data(BIGINT, DATE);")
    op.execute("DROP FUNCTION IF EXISTS calculate_all_performance_metrics(NUMERIC, NUMERIC, NUMERIC, NUMERIC, NUMERIC, NUMERIC, NUMERIC, NUMERIC, NUMERIC, NUMERIC, NUMERIC);")
    op.execute("DROP FUNCTION IF EXISTS calculate_relative_performance(NUMERIC, NUMERIC);")
    op.execute("DROP FUNCTION IF EXISTS calculate_rolling_3y_return(BIGINT, DATE);")
    op.execute("DROP FUNCTION IF EXISTS calculate_ath_atl_percentages(NUMERIC, NUMERIC, NUMERIC);")
    op.execute("DROP FUNCTION IF EXISTS calculate_cagr(NUMERIC, NUMERIC, NUMERIC);")
    op.execute("DROP FUNCTION IF EXISTS calculate_return(NUMERIC, NUMERIC);")
