"""create_nav_functions

Revision ID: performance_002
Revises: performance_001
Create Date: 2025-07-11 12:30:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "performance_002"
down_revision: Union[str, None] = "performance_001"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create NAV retrieval functions with holiday-safe fallback logic."""
    
    # 1. Holiday-safe NAV retrieval function (core function)
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_nav_safe(
            p_schemecode BIGINT,
            p_target_date DATE
        ) RETURNS NUMERIC(18,6) AS $$
        DECLARE
            nav_value NUMERIC(18,6);
        BEGIN
            -- Get the most recent NAV on or before the target date
            -- This handles weekends and market holidays automatically
            SELECT navrs INTO nav_value
            FROM navhist
            WHERE schemecode = p_schemecode
              AND navdate <= p_target_date
              AND navrs > 0  -- Exclude invalid NAV values
            ORDER BY navdate DESC
            LIMIT 1;
            
            RETURN nav_value;
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )
    
    # 2. Batch NAV retrieval for all required time periods
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_nav_points(
            p_schemecode BIGINT, 
            p_base_date DATE
        ) RETURNS TABLE(
            nav_current NUMERIC(18,6),
            nav_1w NUMERIC(18,6),
            nav_1m NUMERIC(18,6),
            nav_3m NUMERIC(18,6),
            nav_6m NUMERIC(18,6),
            nav_1y NUMERIC(18,6),
            nav_3y NUMERIC(18,6),
            nav_5y NUMERIC(18,6),
            nav_10y NUMERIC(18,6)
        ) AS $$
        BEGIN
            -- Return all NAV points needed for performance calculations
            -- Each call uses holiday-safe retrieval
            RETURN QUERY
            SELECT 
                get_nav_safe(p_schemecode, p_base_date) as nav_current,
                get_nav_safe(p_schemecode, p_base_date - INTERVAL '7 days') as nav_1w,
                get_nav_safe(p_schemecode, p_base_date - INTERVAL '30 days') as nav_1m,
                get_nav_safe(p_schemecode, p_base_date - INTERVAL '90 days') as nav_3m,
                get_nav_safe(p_schemecode, p_base_date - INTERVAL '180 days') as nav_6m,
                get_nav_safe(p_schemecode, p_base_date - INTERVAL '365 days') as nav_1y,
                get_nav_safe(p_schemecode, p_base_date - INTERVAL '3 years') as nav_3y,
                get_nav_safe(p_schemecode, p_base_date - INTERVAL '5 years') as nav_5y,
                get_nav_safe(p_schemecode, p_base_date - INTERVAL '10 years') as nav_10y;
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )
    
    # 3. All-time high and low calculation
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_ath_atl(
            p_schemecode BIGINT,
            p_up_to_date DATE
        ) RETURNS TABLE(
            ath_nav NUMERIC(18,6),
            atl_nav NUMERIC(18,6)
        ) AS $$
        BEGIN
            -- Get all-time high and low NAV up to the specified date
            RETURN QUERY
            SELECT 
                MAX(navrs) as ath_nav,
                MIN(navrs) as atl_nav
            FROM navhist
            WHERE schemecode = p_schemecode 
              AND navdate <= p_up_to_date
              AND navrs > 0;  -- Exclude invalid NAV values
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )
    
    # 4. Latest NAV with date (for current NAV retrieval)
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_latest_nav(
            p_schemecode BIGINT,
            p_up_to_date DATE DEFAULT CURRENT_DATE
        ) RETURNS TABLE(
            nav_date DATE,
            nav_value NUMERIC(18,6)
        ) AS $$
        BEGIN
            -- Get the most recent NAV and its date
            RETURN QUERY
            SELECT 
                navdate as nav_date,
                navrs as nav_value
            FROM navhist
            WHERE schemecode = p_schemecode
              AND navdate <= p_up_to_date
              AND navrs > 0
            ORDER BY navdate DESC
            LIMIT 1;
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )
    
    # 5. NAV availability check (useful for data validation)
    op.execute(
        """
        CREATE OR REPLACE FUNCTION check_nav_availability(
            p_schemecode BIGINT,
            p_required_periods INTEGER[] DEFAULT ARRAY[7, 30, 90, 180, 365, 1095, 1825, 3650]
        ) RETURNS TABLE(
            period_days INTEGER,
            nav_available BOOLEAN,
            nav_date DATE,
            nav_value NUMERIC(18,6)
        ) AS $$
        DECLARE
            period_day INTEGER;
            target_date DATE;
            nav_info RECORD;
        BEGIN
            -- Check NAV availability for required periods
            FOREACH period_day IN ARRAY p_required_periods
            LOOP
                target_date := CURRENT_DATE - (period_day || ' days')::INTERVAL;
                
                SELECT * INTO nav_info 
                FROM get_latest_nav(p_schemecode, target_date);
                
                RETURN QUERY
                SELECT 
                    period_day,
                    (nav_info.nav_value IS NOT NULL) as nav_available,
                    nav_info.nav_date,
                    nav_info.nav_value;
            END LOOP;
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )
    
    # 6. Scheme metadata retrieval (for fund information)
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_scheme_metadata(
            p_schemecode BIGINT
        ) RETURNS TABLE(
            fund_name TEXT,
            asset_class TEXT,
            investment_objective TEXT,
            aum NUMERIC(18,6)
        ) AS $$
        BEGIN
            -- Get scheme metadata from multiple tables
            RETURN QUERY
            SELECT 
                sm.scheme_name as fund_name,
                sc.asset_type as asset_class,
                sc.category as investment_objective,
                COALESCE(sa.total, 0) as aum
            FROM scheme_master sm
            LEFT JOIN scheme_details sd ON sm.schemecode = sd.schemecode
            LEFT JOIN sclass_mst sc ON sd.classcode = sc.classcode
            LEFT JOIN (
                SELECT DISTINCT ON (schemecode) 
                    schemecode, total
                FROM scheme_aum 
                WHERE date <= CURRENT_DATE
                ORDER BY schemecode, date DESC
            ) sa ON sm.schemecode = sa.schemecode
            WHERE sm.schemecode = p_schemecode;
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
    )


def downgrade() -> None:
    """Drop NAV functions."""
    op.execute("DROP FUNCTION IF EXISTS get_scheme_metadata(BIGINT);")
    op.execute("DROP FUNCTION IF EXISTS check_nav_availability(BIGINT, INTEGER[]);")
    op.execute("DROP FUNCTION IF EXISTS get_latest_nav(BIGINT, DATE);")
    op.execute("DROP FUNCTION IF EXISTS get_ath_atl(BIGINT, DATE);")
    op.execute("DROP FUNCTION IF EXISTS get_nav_points(BIGINT, DATE);")
    op.execute("DROP FUNCTION IF EXISTS get_nav_safe(BIGINT, DATE);")
