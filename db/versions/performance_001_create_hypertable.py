"""create_performance_hypertable

Revision ID: performance_001
Revises: ff4656e309fe
Create Date: 2025-07-11 12:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "performance_001"
down_revision: Union[str, None] = "d71096870aad"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create TimescaleDB hypertable for performance snapshots."""

    # Enable TimescaleDB extension
    op.execute("CREATE EXTENSION IF NOT EXISTS timescaledb;")

    # Create performance snapshots table
    op.create_table(
        "performance_snapshots",
        # Primary identification fields
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            nullable=False,
            comment="Unique fund identifier",
        ),
        sa.Column(
            "date",
            sa.Date(),
            nullable=False,
            comment="Snapshot date (daily granularity)",
        ),
        # Metadata fields
        sa.Column(
            "fund_name",
            sa.String(length=255),
            nullable=True,
            comment="Name of the mutual fund scheme",
        ),
        sa.Column(
            "aum",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Assets Under Management on snapshot date",
        ),
        sa.Column(
            "asset_class",
            sa.String(length=100),
            nullable=True,
            comment="Equity, Debt, Hybrid, etc.",
        ),
        sa.Column(
            "investment_objective",
            sa.String(length=255),
            nullable=True,
            comment="Stated objective (Growth, Income, Balanced)",
        ),
        # Raw NAV field
        sa.Column(
            "nav",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Net Asset Value on snapshot date",
        ),
        # Short-term performance metrics (1W, 1M)
        sa.Column(
            "perf_1w", sa.Numeric(18, 6), nullable=True, comment="1-week return (%)"
        ),
        sa.Column(
            "perf_1m", sa.Numeric(18, 6), nullable=True, comment="1-month return (%)"
        ),
        # Medium-term performance metrics (3M, 6M, 1Y)
        sa.Column(
            "perf_3m", sa.Numeric(18, 6), nullable=True, comment="3-month return (%)"
        ),
        sa.Column(
            "perf_6m", sa.Numeric(18, 6), nullable=True, comment="6-month return (%)"
        ),
        sa.Column(
            "perf_1y", sa.Numeric(18, 6), nullable=True, comment="1-year return (%)"
        ),
        # Absolute returns (alternative representation)
        sa.Column(
            "absret_1y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Absolute return over 1 year (%)",
        ),
        sa.Column(
            "absret_3m",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Absolute return over 3 months (%)",
        ),
        sa.Column(
            "absret_6m",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Absolute return over 6 months (%)",
        ),
        # Long-term CAGR metrics
        sa.Column(
            "cagr_3y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Compounded Annual Growth Rate over 3 years (%)",
        ),
        sa.Column(
            "cagr_5y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Compounded Annual Growth Rate over 5 years (%)",
        ),
        sa.Column(
            "cagr_10y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Compounded Annual Growth Rate over 10 years (%)",
        ),
        # Rolling return metrics
        sa.Column(
            "rolling_3y_avg_ret",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Avg annualized return over rolling 3-year windows (%)",
        ),
        # Relative performance vs sub-category
        sa.Column(
            "ret_vs_subcat_1y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Return vs sub-category avg (1Y) (%)",
        ),
        sa.Column(
            "ret_vs_subcat_3y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Return vs sub-category avg (3Y) (%)",
        ),
        sa.Column(
            "ret_vs_subcat_5y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Return vs sub-category avg (5Y) (%)",
        ),
        sa.Column(
            "ret_vs_subcat_10y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Return vs sub-category avg (10Y) (%)",
        ),
        # All-time high/low metrics
        sa.Column(
            "percent_from_ath",
            sa.Numeric(18, 6),
            nullable=True,
            comment="% difference from all-time high NAV (%)",
        ),
        sa.Column(
            "percent_from_atl",
            sa.Numeric(18, 6),
            nullable=True,
            comment="% difference from all-time low NAV (%)",
        ),
        # Audit fields
        sa.Column(
            "created_at",
            sa.DateTime(),
            nullable=False,
            server_default=sa.text("CURRENT_TIMESTAMP"),
            comment="Record creation timestamp",
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(),
            nullable=False,
            server_default=sa.text("CURRENT_TIMESTAMP"),
            comment="Record update timestamp",
        ),
        sa.Column(
            "flag",
            sa.String(length=1),
            nullable=True,
            comment="Status flag for soft deletes",
        ),
        # Composite primary key for time-series data
        sa.PrimaryKeyConstraint("schemecode", "date"),
    )

    # Convert to TimescaleDB hypertable with optimal partitioning
    op.execute(
        """
        SELECT create_hypertable(
            'performance_snapshots', 
            'date',
            chunk_time_interval => INTERVAL '1 month',
            partitioning_column => 'schemecode',
            number_partitions => 50
        );
        """
    )

    # Note: Compression can be added later with:
    # SELECT add_compression_policy('performance_snapshots', INTERVAL '7 days');

    # Create optimized indexes for time-series queries
    # Note: TimescaleDB hypertables don't support CONCURRENT index creation
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS performance_snapshots_schemecode_idx
        ON performance_snapshots (schemecode);
        """
    )

    op.execute(
        """
        CREATE INDEX IF NOT EXISTS performance_snapshots_date_idx
        ON performance_snapshots (date DESC);
        """
    )

    op.execute(
        """
        CREATE INDEX IF NOT EXISTS performance_snapshots_asset_class_idx
        ON performance_snapshots (asset_class) WHERE asset_class IS NOT NULL;
        """
    )

    # Performance metric indexes for ranking/filtering
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS performance_snapshots_perf_1y_idx
        ON performance_snapshots (perf_1y DESC) WHERE perf_1y IS NOT NULL;
        """
    )

    op.execute(
        """
        CREATE INDEX IF NOT EXISTS performance_snapshots_cagr_3y_idx
        ON performance_snapshots (cagr_3y DESC) WHERE cagr_3y IS NOT NULL;
        """
    )

    op.execute(
        """
        CREATE INDEX IF NOT EXISTS performance_snapshots_cagr_5y_idx
        ON performance_snapshots (cagr_5y DESC) WHERE cagr_5y IS NOT NULL;
        """
    )

    # Composite indexes for common query patterns
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS performance_snapshots_date_asset_class_idx
        ON performance_snapshots (date DESC, asset_class);
        """
    )

    op.execute(
        """
        CREATE INDEX IF NOT EXISTS performance_snapshots_schemecode_date_idx
        ON performance_snapshots (schemecode, date DESC);
        """
    )


def downgrade() -> None:
    """Drop performance hypertable."""
    op.execute("DROP TABLE IF EXISTS performance_snapshots CASCADE;")
