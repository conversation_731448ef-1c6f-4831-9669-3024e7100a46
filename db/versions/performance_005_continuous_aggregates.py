"""create_continuous_aggregates

Revision ID: performance_005
Revises: performance_004
Create Date: 2025-07-11 14:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "performance_005"
down_revision: Union[str, None] = "performance_004"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create TimescaleDB continuous aggregates for real-time analytics."""

    # Note: Continuous aggregates require autocommit mode
    # For now, we'll skip continuous aggregates and add them manually later
    # This is because Alembic runs in transaction mode which conflicts with continuous aggregates

    # TODO: Create continuous aggregates manually after migration:
    # 1. Category averages - auto-updating category performance benchmarks
    # CREATE MATERIALIZED VIEW performance_category_averages
    # WITH (timescaledb.continuous) AS
    # SELECT time_bucket('1 day', date) as day, asset_class, AVG(perf_1y) as avg_perf_1y, ...
    # FROM performance_snapshots WHERE asset_class IS NOT NULL GROUP BY day, asset_class;

    pass  # Skip continuous aggregates for now


def downgrade() -> None:
    """Drop continuous aggregates and related objects."""
    # No continuous aggregates to drop in this migration
    pass
